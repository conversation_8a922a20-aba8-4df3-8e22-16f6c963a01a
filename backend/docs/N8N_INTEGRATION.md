# N8N Integration Implementation Guide

## Table of Contents

1.  [Architectural Principles](#architectural-principles)
    1.1. [Modularity of N8N Integration](#modularity-of-n8n-integration)
    1.2. [Logging](#logging)
    1.3. [Error Handling](#error-handling)
    1.4. [Pre-requisites and Assumptions](#pre-requisites-and-assumptions)
2.  [Business Logic Requirements](#business-logic-requirements)
    2.1. [Office Hours](#office-hours)
    2.2. [Chat Routing Logic](#chat-routing-logic)
    2.3. [Escalation Flow](#escalation-flow)
3.  [Implementation Phases](#implementation-phases)
    3.1. [Phase 1: Core Infrastructure](#phase-1-core-infrastructure)
    3.1.1. [Environment Configuration](#environment-configuration)
    3.1.2. [Database Schema Updates](#database-schema-updates)
    3.1.3. [Office Hours Service](#office-hours-service)
    3.1.4. [N8N Integration Service](#n8n-integration-service)
    3.2. [Phase 2: Enhanced Chat Service Integration](#phase-2-enhanced-chat-service-integration)
    3.2.1. [Updated Chat Service](#updated-chat-service)
    3.2.2. [Operator Entity Enhancements (if needed)](#operator-entity-enhancements)
    3.3. [Phase 3: Incoming N8N API Endpoints](#phase-3-incoming-n8n-api-endpoints)
    3.3.1. [N8N Webhook Controller](#n8n-webhook-controller)
    3.3.2. [N8N Authentication Guard](#n8n-authentication-guard)
    3.4. [Phase 4: Module Configuration](#phase-4-module-configuration)
    3.4.1. [CommonAppModule (for OfficeHoursService)](#commonappmodule)
    3.4.2. [N8nIntegrationModule](#n8nintegrationmodule)
    3.4.3. [ChatModule Updates](#chatmodule-updates)
    3.4.4. [AppModule Updates](#appmodule-updates)

    [Deployment Notes](#deployment-notes)

## 1. Architectural Principles

### 1.1 Modularity of N8N Integration

- **Core Principle**: The n8n integration is designed to be modular. The core chat system (user-to-operator chat) MUST remain fully functional even if n8n integration is disabled or not configured (e.g., `N8N_WEBHOOK_URL` is not set, or a master `ENABLE_N8N_INTEGRATION` flag is false).
- **Graceful Degradation**: Services interacting with n8n (primarily `ChatService` calling `N8nIntegrationService`) MUST gracefully handle scenarios where n8n is unavailable or unconfigured. This means logging a warning and continuing with the primary chat flow without throwing errors that would disrupt the user experience.
- **Fallback Logic (N8N Disabled)**: If n8n is not configured/disabled, and routing logic determines a 'bot' should handle the chat, the system should:
  - If within office hours but no operators available: Inform the user they are in a queue and an operator will attend shortly. `current_handler` could be set to 'queued_for_operator'.
  - If outside office hours: Inform the user that no operators are available and to try again during office hours, or allow them to leave an offline message (if that feature exists/is planned separately). `current_handler` could be set to 'offline_pending'.
  - The `shouldRouteToBot` flag from `determineInitialRouting` will still be relevant, but its implication changes if n8n is off. The `ChatService` will need to implement this fallback behavior.

### 1.2 Logging

- **Guideline**: Implement consistent and detailed logging for all new services, critical logic paths, and error conditions. Use NestJS `Logger` service. Log relevant context, such as `chatRoomId`, `userId`, `operatorId`, and reasons for routing decisions.

### 1.3 Error Handling

- **Guideline**: Standardize error responses. Use NestJS built-in HTTP exceptions (`HttpException`, `NotFoundException`, etc.) consistently. For domain-specific errors, consider creating custom exception classes. Ensure sensitive error details are not exposed to the client.

### 1.4 Pre-requisites and Assumptions

- `@nestjs/axios` and `@nestjs/config` are installed and configured in the NestJS project.
- TypeORM is configured, and base entities (`ChatRoom`, `Message`, `Operator`, `ChatRoomParticipant`, `User` for chat) exist or will be created/updated as per this plan.
- A basic understanding of NestJS module structure, services, controllers, and guards is assumed for the implementing AI.
- The NestJS application is set up to correctly parse JSON request bodies. For webhook signature validation, raw request bodies might be needed (see [N8N Authentication Guard](#n8n-authentication-guard)).

## 2. Business Logic Requirements

### 2.1 Office Hours

- **Standard Hours**: Sunday to Monday, 08:00 - 17:00 (Asia/Makassar timezone, configurable via `.env`).
- **Holiday Handling**: Even during defined office hours, if the system identifies the current day as an incidental holiday (via API or fallback list), the n8n chatbot serves as the first point of contact.
- **Operator Escalation**: Guests interacting with the chatbot (e.g., during a holiday, outside office hours, or if operators are busy) MUST have a clear option to request a transfer to a live agent. This transfer is contingent on an operator being logged in and available.

### 2.2 Chat Routing Logic

```
┌─────────────────┐
│   New Chat      │
│   Request       │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Check Office    │
│ Hours & Holiday │
│ Status          │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐     No      ┌─────────────────┐
│ Is Office Hours │────────────▶│ Route to n8n    │
│ AND No Holiday? │             │ Chatbot         │
└─────────┬───────┘             └─────────────────┘
          │ Yes
          ▼
┌─────────────────┐     No      ┌─────────────────┐
│ Are Operators   │────────────▶│ Route to n8n    │
│ Available?      │             │ Chatbot         │
└─────────┬───────┘             └─────────────────┘
          │ Yes
          ▼
┌─────────────────┐
│ Route to Human  │
│ Operator        │
└─────────────────┘
```

### 2.3 Escalation Flow

```
┌─────────────────┐
│ Guest in n8n    │
│ Chat Session    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Guest Requests  │
│ Human Agent     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐     No      ┌─────────────────┐
│ Are Operators   │────────────▶│ Inform Guest:   │
│ Available?      │             │ No agents       │
└─────────┬───────┘             │ available       │
          │ Yes                 └─────────────────┘
          ▼
┌─────────────────┐
│ Transfer Chat   │
│ to Operator     │
└─────────────────┘
```

## 3. Implementation Phases

### 3.1 Phase 1: Core Infrastructure

#### 3.1.1 Environment Configuration

Add the following to `.env.example` and ensure they are loaded by `ConfigModule`. Comments should guide the user filling them.

```env
# --- N8N Integration ---
# Master switch for N8N integration. If false, N8N related calls will be skipped.
ENABLE_N8N_INTEGRATION=true
# URL for the N8N webhook that NestJS will call to send chat events.
N8N_WEBHOOK_URL=http://localhost:5678/webhook/your_chat_workflow_endpoint
# API Key for authenticating NestJS requests to the N8N_WEBHOOK_URL (if N8N webhook is secured).
N8N_OUTGOING_API_KEY=your_n8n_api_key_for_nestjs_to_use
# Secret used by NestJS to sign outgoing requests to N8N (optional, if N8N verifies this).
# N8N_OUTGOING_WEBHOOK_SECRET=your_secret_for_signing_outgoing_webhooks
# Secret used by NestJS to verify incoming webhook calls from N8N. N8N must include a matching signature.
N8N_INCOMING_WEBHOOK_SECRET=your_secret_for_n8n_to_call_nestjs_securely

# --- Office Hours Configuration ---
# IANA timezone string for office hours calculation (e.g., Asia/Makassar, America/New_York).
OFFICE_TIMEZONE=Asia/Makassar
# Start hour of office operations (24-hour format, e.g., 8 for 8 AM).
OFFICE_START_HOUR=8
# End hour of office operations (24-hour format, e.g., 17 for 5 PM. Chat initiated before this hour is considered within office hours).
OFFICE_END_HOUR=17
# Comma-separated list of working days. Sunday=0, Monday=1, ..., Saturday=6.
OFFICE_DAYS=0,1

# --- Holiday API Configuration (Optional) ---
# URL for an external API to fetch holiday list. If not provided, system falls back to hardcoded list.
HOLIDAY_API_URL=https://api.example.com/holidays
# API Key for the HOLIDAY_API_URL, if required by the API.
HOLIDAY_API_KEY=your_holiday_api_key_if_needed
```

#### 3.1.2 Database Schema Updates

**`ChatRoom` Entity (`backend/src/modules/chat/entities/chat-room.entity.ts`)**
The following fields have been added (ensure TypeORM decorators are correct):

```typescript
@Column({
  default: 'unassigned',
  comment: 'operator, bot, unassigned, pending_assignment',
})
current_handler: string;

@Column({
  nullable: true,
})
handler_assigned_at: Date;
```

#### 3.1.3 Office Hours Service

**File Location**: `backend/src/common/services/office-hours.service.ts`
**Module**: This service should be part of a `CommonAppModule` (see [Phase 4](#commonappmodule)).

**Purpose**: Determines if the current time falls within defined office hours and checks for holidays.

```typescript
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface OfficeHoursConfig {
  timezone: string;
  startHour: number;
  endHour: number;
  workingDays: number[]; // 0=Sunday, 1=Monday, etc.
}

export interface HolidayCheck {
  isHoliday: boolean;
  holidayName?: string;
  reason?: string;
}

@Injectable()
export class OfficeHoursService {
  private readonly logger = new Logger(OfficeHoursService.name);
  private readonly config: OfficeHoursConfig;

  constructor(private configService: ConfigService) {
    let parsedWorkingDays: number[] = [];
    try {
      const daysString = this.configService.get<string>('OFFICE_DAYS', '0,1');
      parsedWorkingDays = daysString
        .split(',')
        .map((day) => parseInt(day.trim()))
        .filter((day) => !isNaN(day) && day >= 0 && day <= 6);
      if (parsedWorkingDays.length === 0 && daysString.length > 0) {
        this.logger.warn(
          `OFFICE_DAYS environment variable "${daysString}" parsed to an empty list. Defaulting to no working days.`,
        );
      } else if (parsedWorkingDays.length === 0) {
        this.logger.warn(
          `OFFICE_DAYS environment variable is empty or invalid. Defaulting to no working days.`,
        );
      }
    } catch (e) {
      this.logger.error(
        `Error parsing OFFICE_DAYS. Defaulting to no working days. Error: ${e.message}`,
      );
      parsedWorkingDays = [];
    }

    this.config = {
      timezone: this.configService.get<string>(
        'OFFICE_TIMEZONE',
        'Asia/Makassar',
      ),
      startHour: parseInt(
        this.configService.get<string>('OFFICE_START_HOUR', '8'),
        10,
      ),
      endHour: parseInt(
        this.configService.get<string>('OFFICE_END_HOUR', '17'),
        10,
      ),
      workingDays: parsedWorkingDays,
    };
    this.logger.log(
      `OfficeHoursService initialized with config: ${JSON.stringify(this.config)}`,
    );
  }

  /**
   * Check if current time is within office hours.
   * Note: date.toLocaleString('en-US', ...) can be sensitive to the server's system locale if 'en-US' is not available.
   * For robust multi-server deployments, consider using a dedicated date/timezone library like 'date-fns-tz' or 'luxon' if issues arise.
   */
  isWithinOfficeHours(date: Date = new Date()): boolean {
    try {
      const officeTime = new Date(
        date.toLocaleString('en-US', { timeZone: this.config.timezone }),
      );
      const currentDay = officeTime.getDay(); // Sunday = 0, Monday = 1, ...
      const currentHour = officeTime.getHours();

      const isWorkingDay = this.config.workingDays.includes(currentDay);
      const isWorkingHour =
        currentHour >= this.config.startHour &&
        currentHour < this.config.endHour;

      this.logger.debug(
        `Office hours check for date ${date.toISOString()} (TZ: ${this.config.timezone}): Day=${currentDay}, Hour=${currentHour}. IsWorkingDay=${isWorkingDay}, IsWorkingHour=${isWorkingHour}. Config: Start=${this.config.startHour}, End=${this.config.endHour}, Days=${this.config.workingDays.join(',')}`,
      );
      return isWorkingDay && isWorkingHour;
    } catch (error) {
      this.logger.error(
        `Error checking office hours for date ${date.toISOString()}: ${error.message}`,
        error.stack,
      );
      return false; // Fail safe: assume outside office hours on error
    }
  }

  /**
   * Check if today is a holiday.
   * Attempts to fetch from HOLIDAY_API_URL first, then falls back to a local hardcoded list.
   */
  async isHoliday(date: Date = new Date()): Promise<HolidayCheck> {
    const holidayApiUrl = this.configService.get<string>('HOLIDAY_API_URL');
    const holidayApiKey = this.configService.get<string>('HOLIDAY_API_KEY');

    if (holidayApiUrl) {
      try {
        // Placeholder for HttpService. This service needs HttpModule from @nestjs/axios
        // const httpService = new HttpService(); // This should be injected
        // const response = await firstValueFrom(httpService.get(holidayApiUrl, { headers: { 'X-Api-Key': holidayApiKey } }));
        // const holidaysFromApi: Array<{ date: string; name: string }> = response.data;
        // const dateString = date.toISOString().split('T')[0];
        // const apiHoliday = holidaysFromApi.find((h) => h.date === dateString);
        // if (apiHoliday) {
        //   this.logger.log(`Holiday detected from API: ${apiHoliday.name} on ${dateString}`);
        //   return { isHoliday: true, holidayName: apiHoliday.name, reason: 'API' };
        // }
        this.logger.warn(
          'Holiday API check is placeholder logic. Actual HTTP call to be implemented.',
        );
      } catch (apiError) {
        this.logger.warn(
          `Holiday API call to ${holidayApiUrl} failed, falling back to local list. Error: ${apiError.message}`,
        );
      }
    }

    // Fallback to local list
    const localHolidays = await this.getHardcodedHolidayList(date);
    const dateString = date.toISOString().split('T')[0];
    const localHoliday = localHolidays.find((h) => h.date === dateString);

    if (localHoliday) {
      this.logger.log(
        `Holiday detected from local list: ${localHoliday.name} on ${dateString}`,
      );
      return {
        isHoliday: true,
        holidayName: localHoliday.name,
        reason: 'Local List',
      };
    }

    return { isHoliday: false };
  }

  /**
   * Get list of hardcoded holidays for the year.
   * CRITICAL: This hardcoded list is for DEMONSTRATION & FALLBACK ONLY.
   * In a production environment, this method MUST be updated to fetch data from the HOLIDAY_API_URL.
   * If HOLIDAY_API_URL is configured, attempt to fetch from it first. If it fails or is not configured, then use this.
   * Expected holiday API response for a given year/date range: [{ "date": "YYYY-MM-DD", "name": "Holiday Name" }, ...]
   */
  private async getHardcodedHolidayList(
    date: Date,
  ): Promise<Array<{ date: string; name: string }>> {
    const year = date.getFullYear();
    // Example:
    return [
      { date: `${year}-01-01`, name: "New Year's Day (Local Fallback)" },
      { date: `${year}-12-25`, name: 'Christmas Day (Local Fallback)' },
      // Add other significant local holidays if API is unreliable or not used.
    ];
  }

  /**
   * Determine if chat should be handled by operator or bot based on office hours and holiday status.
   */
  async shouldRouteToOperator(date: Date = new Date()): Promise<{
    routeToOperator: boolean;
    reason: string;
    details?: HolidayCheck;
  }> {
    const isOfficeHours = this.isWithinOfficeHours(date);
    const holidayCheck = await this.isHoliday(date);

    if (holidayCheck.isHoliday) {
      const reason = `Holiday: ${holidayCheck.holidayName} (Source: ${holidayCheck.reason})`;
      this.logger.log(`Routing decision: Not to operator. Reason: ${reason}`);
      return { routeToOperator: false, reason, details: holidayCheck };
    }

    if (!isOfficeHours) {
      const reason = `Outside office hours (Current time in ${this.config.timezone}: ${new Date(date.toLocaleString('en-US', { timeZone: this.config.timezone })).toLocaleTimeString()})`;
      this.logger.log(`Routing decision: Not to operator. Reason: ${reason}`);
      return { routeToOperator: false, reason };
    }

    const reason = 'Within office hours and no holiday detected.';
    this.logger.log(`Routing decision: To operator. Reason: ${reason}`);
    return { routeToOperator: true, reason };
  }

  /**
   * Get next available office hours start time from the given date.
   */
  getNextOfficeHours(currentDate: Date = new Date()): Date | null {
    if (this.config.workingDays.length === 0) {
      this.logger.warn(
        'Cannot determine next office hours as no working days are configured.',
      );
      return null;
    }

    const nextOfficeDay = new Date(
      currentDate.toLocaleString('en-US', { timeZone: this.config.timezone }),
    );
    nextOfficeDay.setHours(this.config.startHour, 0, 0, 0); // Set to start of office hours for today

    for (let i = 0; i < 7; i++) {
      // Check next 7 days
      if (
        nextOfficeDay.getTime() >
          new Date(
            currentDate.toLocaleString('en-US', {
              timeZone: this.config.timezone,
            }),
          ).getTime() &&
        this.config.workingDays.includes(nextOfficeDay.getDay())
      ) {
        // Found a future working day's start time
        // Further check if this day is a holiday (optional, could be complex)
        return new Date(
          nextOfficeDay.toLocaleString('en-US', { timeZone: 'UTC' }),
        ); // Return in UTC or consistent TZ
      }
      nextOfficeDay.setDate(nextOfficeDay.getDate() + 1);
      nextOfficeDay.setHours(this.config.startHour, 0, 0, 0);
    }
    this.logger.warn(
      'Could not find next available office hours within the next 7 days.',
    );
    return null; // Should ideally not happen if working days are configured
  }
}
```

#### 3.1.4 N8N Integration Service

**File Location**: `backend/src/modules/n8n-integration/n8n-integration.service.ts`
**Module**: This service will be part of `N8nIntegrationModule` (see [Phase 4](#n8nintegrationmodule)). This module will need to import `HttpModule` from `@nestjs/axios` and `ConfigModule`.

**Purpose**: Handles all outgoing communication to n8n webhooks and provides utilities for n8n integration.

```typescript
import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

import * as crypto from 'crypto'; // Import crypto for signature validation

export interface N8nChatEvent {
  eventType:
    | 'new_chat'
    | 'new_message'
    | 'chat_transferred'
    | 'chat_closed'
    | 'user_escalation_request';
  chatRoomId: string;
  userId?: string; // ID of the end-user
  operatorId?: string; // ID of the operator, if applicable
  message?: {
    id: string; // Message ID from local DB
    content: string; // Plain text content of the message
    sender: 'user' | 'operator'; // Who sent this message being relayed
    timestamp: string; // ISO 8601 string format of when the original message was sent/created
  };
  metadata?: Record<string, any>; // Any additional contextual data
  timestamp: string; // ISO 8601 string, when this event object was created by NestJS
  source: 'nestjs-chat'; // Identifier for the source system
}

export interface N8nBotResponse {
  success: boolean;
  reason?:
    | 'N8N_NOT_CONFIGURED'
    | 'N8N_REQUEST_FAILED'
    | 'N8N_INVALID_RESPONSE'
    | string; // For diagnostics
  messageId?: string; // If n8n created a message, its ID in n8n (optional)
  content?: string; // If n8n wants to reply directly via this sync call (less common for webhooks)
  actions?: Array<{
    // Actions n8n might suggest NestJS to take (optional)
    type:
      | 'transfer_to_operator'
      | 'close_chat'
      | 'escalate'
      | 'send_quick_reply';
    data?: Record<string, any>;
  }>;
  error?: string; // Error message if success is false
}

@Injectable()
export class N8nIntegrationService {
  private readonly logger = new Logger(N8nIntegrationService.name);
  private readonly isN8nEnabled: boolean;
  private readonly n8nWebhookUrl: string;
  private readonly n8nOutgoingApiKey: string;
  // private readonly n8nOutgoingWebhookSecret: string; // For signing outgoing requests, if needed by N8N
  private readonly n8nIncomingWebhookSecret: string; // For validating incoming requests from N8N

  constructor(
    private configService: ConfigService,
    private httpService: HttpService, // Ensure HttpModule is imported in N8nIntegrationModule
  ) {
    this.isN8nEnabled = this.configService.get<boolean>(
      'ENABLE_N8N_INTEGRATION',
      false,
    );
    this.n8nWebhookUrl = this.configService.get<string>('N8N_WEBHOOK_URL');
    this.n8nOutgoingApiKey = this.configService.get<string>(
      'N8N_OUTGOING_API_KEY',
    );
    // this.n8nOutgoingWebhookSecret = this.configService.get<string>('N8N_OUTGOING_WEBHOOK_SECRET');
    this.n8nIncomingWebhookSecret = this.configService.get<string>(
      'N8N_INCOMING_WEBHOOK_SECRET',
    );

    if (this.isN8nEnabled && !this.n8nWebhookUrl) {
      this.logger.error(
        'N8N_INTEGRATION is ENABLED but N8N_WEBHOOK_URL is not configured. N8N calls will fail.',
      );
    } else if (this.isN8nEnabled) {
      this.logger.log(
        `N8N Integration Service initialized. Enabled: ${this.isN8nEnabled}, Webhook URL: ${this.n8nWebhookUrl}`,
      );
    } else {
      this.logger.log(
        'N8N Integration Service initialized. N8N Integration is DISABLED.',
      );
    }
  }

  /**
   * Send a chat-related event to the configured N8N webhook.
   * This method handles the actual HTTP POST request to N8N.
   */
  async sendChatEvent(
    eventData: Omit<N8nChatEvent, 'timestamp' | 'source'>,
  ): Promise<N8nBotResponse> {
    if (!this.isN8nEnabled || !this.n8nWebhookUrl) {
      const reason = this.isN8nEnabled
        ? 'N8N_WEBHOOK_URL_NOT_CONFIGURED'
        : 'N8N_DISABLED';
      this.logger.warn(
        `Skipping N8N event (${eventData.eventType}) because: ${reason}. Chat flow continues locally.`,
      );
      return { success: false, reason };
    }

    const payload: N8nChatEvent = {
      ...eventData,
      timestamp: new Date().toISOString(),
      source: 'nestjs-chat',
    };

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    if (this.n8nOutgoingApiKey) {
      headers['X-API-Key'] = this.n8nOutgoingApiKey; // Or 'Authorization: Bearer ...' depending on N8N setup
    }
    // If signing outgoing requests:
    // if (this.n8nOutgoingWebhookSecret) {
    //   const signature = crypto.createHmac('sha256', this.n8nOutgoingWebhookSecret).update(JSON.stringify(payload)).digest('hex');
    //   headers['X-NestJS-Signature'] = signature;
    // }

    this.logger.debug(
      `Sending event to N8N (${this.n8nWebhookUrl}): ${payload.eventType}`,
      payload,
    );

    try {
      const response = await firstValueFrom(
        this.httpService.post(this.n8nWebhookUrl, payload, {
          headers,
          timeout: 5000 /* Example timeout */,
        }),
      );

      this.logger.log(
        `N8N response received for ${payload.eventType} (Status: ${response.status}):`,
        response.data,
      );

      // N8N webhook responses might not always have a structured N8nBotResponse.
      // Adapt this based on actual N8N workflow response.
      if (response.status >= 200 && response.status < 300) {
        return {
          success: true,
          ...(typeof response.data === 'object'
            ? response.data
            : { rawResponse: response.data }),
        };
      } else {
        this.logger.error(
          `N8N returned non-success status ${response.status} for ${payload.eventType}. Response: ${JSON.stringify(response.data)}`,
        );
        return {
          success: false,
          reason: `N8N_HTTP_ERROR_${response.status}`,
          error: JSON.stringify(response.data),
        };
      }
    } catch (error) {
      const errorMessage = error.response?.data || error.message;
      this.logger.error(
        `Failed to send event to N8N (${payload.eventType}): ${errorMessage}`,
        error.stack,
      );
      if (error.isAxiosError && error.response) {
        this.logger.error('N8N error response details:', error.response.data);
        return {
          success: false,
          reason: 'N8N_REQUEST_FAILED',
          error: JSON.stringify(error.response.data),
        };
      }
      return {
        success: false,
        reason: 'N8N_REQUEST_FAILED',
        error: error.message,
      };
    }
  }

  // --- Specific Event Handler Methods ---

  async handleNewChat(
    chatRoomId: string,
    userId: string,
    initialMessage?: string,
  ): Promise<N8nBotResponse> {
    return this.sendChatEvent({
      eventType: 'new_chat',
      chatRoomId,
      userId,
      metadata: { initialMessage },
    });
  }

  async handleNewMessage(
    chatRoomId: string,
    messageId: string,
    content: string,
    senderId: string,
    senderType: 'user' | 'operator',
    originalTimestamp: string,
  ): Promise<N8nBotResponse> {
    return this.sendChatEvent({
      eventType: 'new_message',
      chatRoomId,
      userId: senderType === 'user' ? senderId : undefined,
      operatorId: senderType === 'operator' ? senderId : undefined,
      message: {
        id: messageId,
        content,
        sender: senderType,
        timestamp: originalTimestamp,
      },
    });
  }

  async handleChatTransferToOperator(
    chatRoomId: string,
    operatorId: string,
    reason?: string,
    transferredByUserId?: string,
  ): Promise<N8nBotResponse> {
    return this.sendChatEvent({
      eventType: 'chat_transferred',
      chatRoomId,
      operatorId,
      userId: transferredByUserId, // User who might have initiated or is part of the chat being transferred
      metadata: { reason, transferType: 'bot_to_operator' },
    });
  }

  async handleUserEscalationRequest(
    chatRoomId: string,
    userId: string,
    messageContent?: string,
  ): Promise<N8nBotResponse> {
    return this.sendChatEvent({
      eventType: 'user_escalation_request',
      chatRoomId,
      userId,
      metadata: { messageContent },
    });
  }

  async handleChatClosure(
    chatRoomId: string,
    closedBy: 'user' | 'operator' | 'system' | 'bot',
    closerId?: string,
  ): Promise<N8nBotResponse> {
    return this.sendChatEvent({
      eventType: 'chat_closed',
      chatRoomId,
      userId: closedBy === 'user' && closerId ? closerId : undefined,
      operatorId: closedBy === 'operator' && closerId ? closerId : undefined,
      metadata: { closedBy },
    });
  }

  /**
   * Validates an incoming webhook signature from N8N.
   * IMPORTANT: 'payloadString' must be the raw, unparsed request body string.
   * NestJS's default body parsing might consume the stream. Ensure the raw body is available.
   */
  validateIncomingWebhookSignature(
    payloadString: string,
    signatureFromHeader: string,
  ): boolean {
    if (!this.n8nIncomingWebhookSecret) {
      this.logger.warn(
        'N8N_INCOMING_WEBHOOK_SECRET not configured. Skipping signature validation. THIS IS INSECURE.',
      );
      return true; // Or false if strict security is needed even without config
    }
    if (!signatureFromHeader) {
      this.logger.warn('Missing signature in incoming N8N webhook header.');
      return false;
    }

    try {
      const expectedSignature = crypto
        .createHmac('sha256', this.n8nIncomingWebhookSecret)
        .update(payloadString)
        .digest('hex');

      const isValid = crypto.timingSafeEqual(
        Buffer.from(signatureFromHeader),
        Buffer.from(expectedSignature),
      );
      if (!isValid) {
        this.logger.warn(
          `Invalid N8N webhook signature. Expected: ${expectedSignature}, Got: ${signatureFromHeader}`,
        );
      }
      return isValid;
    } catch (error) {
      this.logger.error(
        'Error validating N8N incoming webhook signature:',
        error.message,
        error.stack,
      );
      return false;
    }
  }
}
```

### 3.2 Phase 2: Enhanced Chat Service Integration

#### 3.2.1 Updated Chat Service

**File Location**: `backend/src/modules/chat/services/chat.service.ts`
**Module**: `ChatModule` (see [Phase 4](#chatmodule-updates)).
**Key Changes**: Integrate `OfficeHoursService` and `N8nIntegrationService` for routing decisions and event notifications. Gracefully handle disabled N8N integration.

```typescript
// Add these imports at the top (adjust paths as necessary)
import { OfficeHoursService, HolidayCheck } from '../../../common/services/office-hours.service';
import { N8nIntegrationService, N8nBotResponse } from '../../n8n-integration/n8n-integration.service';
// ... other existing imports like Repositories, DTOs, Entities, Gateway, EncryptionService

// Add to constructor's parameters:
// private readonly officeHoursService: OfficeHoursService,
// private readonly n8nIntegrationService: N8nIntegrationService,

// Ensure these are correctly provided in ChatModule.

// Enhanced initiateChatSession method
async initiateChatSession(
  initiateDto: InitiateChatDto,
  userAgent?: string,
  ipAddress?: string,
): Promise<{ chatRoom: ChatRoom; user: ChatUser; initialHandler: string; n8nNotified: boolean }> {
  this.logger.log(`Initiating chat session for user: ${initiateDto.name}`);
  try {
    const user = await this.createOrUpdateUser(initiateDto, userAgent, ipAddress);
    const routingDecision = await this.determineInitialRouting(new Date());
    let finalHandler = routingDecision.handler;
    let n8nNotified = false;

    const chatRoomEntity = this.chatRoomRepository.create({
      type: 'user-operator', // Standard type for user-initiated chats
      status: 'active',      // Initial status
      current_handler: finalHandler, // Determined by routing logic
      handler_assigned_at: new Date(),
      created_at: new Date(),
      updated_at: new Date(),
      // Any other necessary fields from initiateDto or defaults
    });
    const savedChatRoom = await this.chatRoomRepository.save(chatRoomEntity);
    this.logger.log(`Chat room ${savedChatRoom.id} created. Initial handler: ${finalHandler}, Reason: ${routingDecision.reason}`);

    const participant = this.participantRepository.create({
      chat_room_id: savedChatRoom.id,
      user_id: user.id,
      role: 'user',
      joined_at: new Date(),
    });
    await this.participantRepository.save(participant);
    this.logger.log(`User ${user.id} added as participant to chat room ${savedChatRoom.id}`);

    if (routingDecision.routeToBot) {
      const n8nResponse = await this.n8nIntegrationService.handleNewChat(
        savedChatRoom.id,
        user.id,
        initiateDto.initial_message, // Send initial message if available
      );
      n8nNotified = n8nResponse.success;

      if (!n8nResponse.success) {
        this.logger.warn(`N8N new_chat event failed for ${savedChatRoom.id}. Reason: ${n8nResponse.reason}. Chat continues with handler: ${finalHandler}.`);
        // If N8N is explicitly disabled or URL not configured, and routing said 'bot',
        // we need to adjust the handler to a non-bot state.
        if (n8nResponse.reason === 'N8N_DISABLED' || n8nResponse.reason === 'N8N_WEBHOOK_URL_NOT_CONFIGURED') {
          if (routingDecision.reason === 'No operators available' || routingDecision.reason.startsWith('Holiday')) {
            finalHandler = 'queued_for_operator'; // Or a more specific state like 'holiday_queue'
            await this.sendSystemMessage(savedChatRoom.id, "Thank you for reaching out. All our operators are currently busy, or it's a holiday. You've been placed in a queue.");
          } else if (routingDecision.reason === 'Outside office hours') {
            finalHandler = 'offline_pending';
            await this.sendSystemMessage(savedChatRoom.id, "Thank you for reaching out. We are currently outside office hours. Please try again later or leave a message.");
          } else {
            // Default fallback if bot was intended but N8N is off for other reasons
            finalHandler = 'pending_assignment';
          }
          await this.chatRoomRepository.update(savedChatRoom.id, { current_handler: finalHandler, handler_assigned_at: new Date() });
          this.logger.log(`Chat room ${savedChatRoom.id} handler updated to ${finalHandler} due to N8N being unavailable.`);
        }
      } else {
         this.logger.log(`N8N successfully notified for new chat ${savedChatRoom.id}.`);
      }
    } else {
        // Routed to operator/pending_assignment directly
        this.logger.log(`Chat ${savedChatRoom.id} routed directly to ${finalHandler}. No N8N notification needed at this stage.`);
        // Potentially notify operators about a new pending chat.
    }

    return {
      chatRoom: await this.chatRoomRepository.findOneBy({ id: savedChatRoom.id }), // Re-fetch to get updated handler
      user,
      initialHandler: finalHandler,
      n8nNotified,
    };
  } catch (error) {
    this.logger.error(`Error initiating chat session for ${initiateDto.name}: ${error.message}`, error.stack);
    throw new HttpException('Failed to initiate chat session', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

// New method for routing determination
private async determineInitialRouting(currentDate: Date): Promise<{
  handler: 'bot' | 'pending_assignment' | 'queued_for_operator' | 'offline_pending'; // More specific handler states
  routeToBot: boolean; // True if the ideal initial handler is the bot
  reason: string;
  holidayDetails?: HolidayCheck;
}> {
  this.logger.debug(`Determining initial routing for date: ${currentDate.toISOString()}`);
  try {
    const officeHoursCheck = await this.officeHoursService.shouldRouteToOperator(currentDate);

    if (!officeHoursCheck.routeToOperator) { // Outside office hours or Holiday
      const reason = officeHoursCheck.reason;
      this.logger.log(`Initial routing: to Bot. Reason: ${reason}`);
      return {
        handler: 'bot', // Ideal handler is bot
        routeToBot: true,
        reason: reason,
        holidayDetails: officeHoursCheck.details,
      };
    }

    // Within office hours and not a holiday, check operator availability
    const availableOperators = await this.getAvailableOperators();
    if (availableOperators.length === 0) {
      const reason = 'No operators available during office hours';
      this.logger.log(`Initial routing: to Bot. Reason: ${reason}`);
      return {
        handler: 'bot', // Ideal handler is bot (as operators are busy)
        routeToBot: true,
        reason: reason,
      };
    }

    const reason = 'Operators available during office hours';
    this.logger.log(`Initial routing: to Operator (pending assignment). Reason: ${reason}`);
    return {
      handler: 'pending_assignment', // Assign to operator queue
      routeToBot: false,
      reason: reason,
    };
  } catch (error) {
    this.logger.error(`Error in determineInitialRouting: ${error.message}`, error.stack);
    // Default to bot on error, to ensure chat can still be captured by N8N if configured
    return {
      handler: 'bot',
      routeToBot: true,
      reason: 'Error in routing logic - defaulting to bot handling',
    };
  }
}

// Enhanced sendMessage method
async sendMessage(sendMessageDto: SendMessageDto, senderId: string, senderType: 'user' | 'operator'): Promise<Message> {
  this.logger.debug(`sendMessage called by ${senderType} ${senderId} for room ${sendMessageDto.chat_room_id}`);
  try {
    const chatRoom = await this.chatRoomRepository.findOne({ where: { id: sendMessageDto.chat_room_id }});
    if (!chatRoom) throw new HttpException('Chat room not found', HttpStatus.NOT_FOUND);

    // Decrypt content before sending to N8N if it was encrypted for storage by user/operator
    // For this example, assuming sendMessageDto.content is plain text from client,
    // and encryption happens before DB save in createMessage.
    // If message.content is already encrypted, it needs decryption before sending to N8N.
    // Let's assume createMessage returns the message with plain text content for now for N8N.
    const message = await this.createMessage(sendMessageDto, senderId, senderType); // This should handle encryption before saving

    // If chat is currently handled by 'bot' and the sender is a 'user', forward to N8N
    if (chatRoom.current_handler === 'bot' && senderType === 'user') {
      this.logger.log(`Chat room ${chatRoom.id} is bot-handled. Forwarding user message ${message.id} to N8N.`);
      // Ensure message.content sent to N8N is plain text.
      // If createMessage encrypts, you need the plain text version here.
      // Assuming sendMessageDto.content is the plain text to be sent.
      const n8nResponse = await this.n8nIntegrationService.handleNewMessage(
        chatRoom.id,
        message.id, // Local DB message ID
        sendMessageDto.content, // Plain text content
        senderId,
        senderType,
        message.created_at.toISOString() // Original message timestamp
      );
      if (!n8nResponse.success) {
        this.logger.warn(`N8N handleNewMessage event failed for chat ${chatRoom.id}, message ${message.id}. Reason: ${n8nResponse.reason}. Bot may not respond.`);
        // Optionally, inform the user about the bot's temporary unavailability if N8N is configured but failing.
        if (n8nResponse.reason !== 'N8N_DISABLED' && n8nResponse.reason !== 'N8N_WEBHOOK_URL_NOT_CONFIGURED') {
            // await this.sendSystemMessage(chatRoom.id, "I'm having trouble reaching my AI assistant at the moment. Please wait, or an operator will assist you if available.");
        }
      }
    }

    await this.chatRoomRepository.update(chatRoom.id, { last_message_at: new Date(), updated_at: new Date() });

    this.chatGateway.emitToRoom(chatRoom.id, 'new_message', {
      message: this.formatMessageResponse(message), // formatMessageResponse should handle decrypting for client if needed
      chatRoomId: chatRoom.id,
    });
    this.logger.log(`Message ${message.id} sent and emitted to room ${chatRoom.id}`);
    return message;
  } catch (error) {
    this.logger.error(`Error sending message in room ${sendMessageDto.chat_room_id}: ${error.message}`, error.stack);
    throw error; // Re-throw original error or a classified HttpException
  }
}

// New method for handling escalation requests from a user in a bot-managed chat
async requestOperatorEscalation(chatRoomId: string, userId: string, reasonMessage?: string): Promise<{
  success: boolean;
  message: string; // User-facing message
  operatorAssigned: boolean;
  assignedOperatorId?: string;
}> {
  this.logger.log(`Operator escalation requested by user ${userId} for chat room ${chatRoomId}. Reason: ${reasonMessage || 'N/A'}`);
  try {
    const chatRoom = await this.chatRoomRepository.findOneBy({ id: chatRoomId });
    if (!chatRoom) throw new HttpException('Chat room not found', HttpStatus.NOT_FOUND);

    if (chatRoom.current_handler !== 'bot') {
      this.logger.warn(`Escalation request for chat ${chatRoomId} which is not bot-handled (current: ${chatRoom.current_handler}).`);
      return { success: false, message: 'This chat is already being handled or pending an operator.', operatorAssigned: chatRoom.current_handler === 'operator' };
    }

    // Notify N8N that user is requesting escalation (optional, but good for N8N to know)
    await this.n8nIntegrationService.handleUserEscalationRequest(chatRoomId, userId, reasonMessage);

    const availableOperators = await this.getAvailableOperators();
    if (availableOperators.length === 0) {
      this.logger.log(`No operators available for escalation in chat ${chatRoomId}. Informing user.`);
      const noOperatorMsg = 'Maaf, saat ini tidak ada operator yang tersedia. Anda dapat melanjutkan percakapan dengan chatbot atau mencoba lagi nanti.';
      await this.sendSystemMessage(chatRoomId, noOperatorMsg);
      return { success: false, message: noOperatorMsg, operatorAssigned: false };
    }

    const assignedOperator = availableOperators[0]; // TODO: Implement better assignment logic (e.g., round-robin, least busy)
    this.logger.log(`Assigning operator ${assignedOperator.id} to chat ${chatRoomId} due to user escalation.`);

    await this.chatRoomRepository.update(chatRoomId, {
      current_handler: 'operator', // Explicitly set to 'operator'
      handler_assigned_at: new Date(),
      updated_at: new Date(),
    });

    // Ensure operator is a participant
    let operatorParticipant = await this.participantRepository.findOne({ where: { chat_room_id: chatRoomId, operator_id: assignedOperator.id }});
    if (!operatorParticipant) {
        operatorParticipant = this.participantRepository.create({
            chat_room_id: chatRoomId,
            operator_id: assignedOperator.id,
            role: 'operator',
            joined_at: new Date(),
        });
        await this.participantRepository.save(operatorParticipant);
        this.logger.log(`Operator ${assignedOperator.id} added as participant to chat room ${chatRoomId}.`);
    }


    // Notify N8N about the successful transfer (even if N8N initiated it, this confirms it on NestJS side)
    const n8nTransferResponse = await this.n8nIntegrationService.handleChatTransferToOperator(
      chatRoomId,
      assignedOperator.id,
      reasonMessage || 'User requested human agent',
      userId
    );
    if (!n8nTransferResponse.success) {
        this.logger.warn(`N8N event failed for chat transfer ${chatRoomId}. Transfer to operator proceeds locally. Reason: ${n8nTransferResponse.reason}`);
    }

    const welcomeMsg = `Halo! Saya ${assignedOperator.name}, operator yang akan membantu Anda. Ada yang bisa saya bantu?`;
    await this.sendSystemMessage(chatRoomId, welcomeMsg);

    this.chatGateway.emitToRoom(chatRoomId, 'chat_transferred', {
      chatRoomId,
      handlerType: 'operator',
      operator: { id: assignedOperator.id, name: assignedOperator.name },
      message: welcomeMsg,
    });
    this.logger.log(`Chat ${chatRoomId} successfully transferred to operator ${assignedOperator.id}. User notified.`);

    return { success: true, message: `Anda telah terhubung dengan operator ${assignedOperator.name}.`, operatorAssigned: true, assignedOperatorId: assignedOperator.id };
  } catch (error) {
    this.logger.error(`Error handling operator escalation for chat ${chatRoomId}: ${error.message}`, error.stack);
    throw new HttpException('Failed to process escalation request', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

// Helper method to send system messages (e.g., "Operator joined", "No operators available")
private async sendSystemMessage(chatRoomId: string, content: string): Promise<Message> {
  this.logger.log(`Sending system message to room ${chatRoomId}: "${content}"`);
  // Ensure content is encrypted if your DB stores encrypted messages
  const encryptedContent = await this.encryptionService.encrypt(content);

  const systemMessage = this.messageRepository.create({
    chat_room_id: chatRoomId,
    content: encryptedContent,
    sender_type: 'system', // Or 'bot' if it's a bot-specific system message
    message_type: 'text', // Or a special system message type
    created_at: new Date(),
  });
  const savedMessage = await this.messageRepository.save(systemMessage);

  this.chatGateway.emitToRoom(chatRoomId, 'new_message', {
    message: this.formatMessageResponse(savedMessage), // Ensure this decrypts for client if needed
    chatRoomId,
  });
  return savedMessage;
}

// Helper method to get available operators
// CRITICAL: This logic needs to be robust.
private async getAvailableOperators(): Promise<Operator[]> {
  this.logger.debug('Fetching available operators...');
  // This should query the Operator entity based on:
  // 1. `is_online: true` (boolean, indicates active WebSocket connection or recent dashboard activity).
  // 2. `status: 'available'` (enum/string, operator has set themselves as available).
  // 3. `current_chats < max_chats` (numeric, operator not at capacity).
  // These fields (`is_online`, `status`, `current_chats`, `max_chats`) need to exist on the Operator entity.
  // See section [Operator Entity Enhancements](#operator-entity-enhancements).
  // Order by least busy or longest idle for fairness if multiple operators are available.
  const operators = await this.operatorRepository.find({
    where: {
      status: 'available', // Make sure 'available' is a valid status in Operator entity
      is_online: true,     // Make sure 'is_online' is maintained accurately
      // Add condition for current_chats < max_chats if implemented
    },
    order: {
      // Example: prioritize operators with fewer current chats or longest idle time
      // current_assigned_chats: 'ASC', // Requires a field to track this
      last_activity_at: 'ASC', // Or DESC depending on desired priority
    },
  });
  this.logger.debug(`Found ${operators.length} available operators.`);
  return operators;
}

// Method to allow N8N or system to send a message as 'bot'
async sendBotMessage(dto: {
  chat_room_id: string;
  content: string;
  message_type?: string; // e.g., 'text', 'quick_reply', 'card'
  metadata?: any;
}): Promise<Message> {
  this.logger.log(`Bot sending message to room ${dto.chat_room_id}: "${dto.content.substring(0,50)}..."`);
  const chatRoom = await this.chatRoomRepository.findOneBy({ id: dto.chat_room_id });
  if (!chatRoom) throw new HttpException('Chat room not found for bot message', HttpStatus.NOT_FOUND);

  // Ensure content is encrypted if your DB stores encrypted messages
  const encryptedContent = await this.encryptionService.encrypt(dto.content);

  const botMessage = this.messageRepository.create({
    chat_room_id: dto.chat_room_id,
    content: encryptedContent,
    sender_type: 'bot', // Distinct sender type for bot messages
    // sender_id: null, // Or a dedicated bot user ID if you have one
    message_type: dto.message_type || 'text',
    metadata: dto.metadata, // For rich messages like quick replies, cards
    created_at: new Date(),
  });
  const savedMessage = await this.messageRepository.save(botMessage);

  await this.chatRoomRepository.update(dto.chat_room_id, { last_message_at: new Date(), updated_at: new Date() });

  this.chatGateway.emitToRoom(dto.chat_room_id, 'new_message', {
    message: this.formatMessageResponse(savedMessage), // Ensure this decrypts for client if needed
    chatRoomId: dto.chat_room_id,
  });
  this.logger.log(`Bot message ${savedMessage.id} sent and emitted to room ${dto.chat_room_id}`);
  return savedMessage;
}

// Method for N8N to trigger chat closure
async closeChatRoomByBot(chatRoomId: string, reason: string): Promise<ChatRoom> {
    this.logger.log(`Closing chat room ${chatRoomId} by bot. Reason: ${reason}`);
    const chatRoom = await this.chatRoomRepository.findOneBy({ id: chatRoomId });
    if (!chatRoom) throw new HttpException('Chat room not found for bot closure', HttpStatus.NOT_FOUND);

    if (chatRoom.status === 'closed') {
        this.logger.warn(`Chat room ${chatRoomId} is already closed.`);
        return chatRoom;
    }

    chatRoom.status = 'closed';
    chatRoom.current_handler = 'system'; // Or 'bot_closed'
    chatRoom.closed_at = new Date();
    // chatRoom.closed_by_id = 'SYSTEM_BOT_ID'; // If you have a system/bot ID
    chatRoom.closure_reason = reason;
    await this.chatRoomRepository.save(chatRoom);

    // Notify N8N about the closure (if N8N didn't initiate it)
    // This might be redundant if N8N initiated, but good for consistency.
    await this.n8nIntegrationService.handleChatClosure(chatRoomId, 'bot');

    this.chatGateway.emitToRoom(chatRoomId, 'chat_closed', { chatRoomId, reason });
    this.logger.log(`Chat room ${chatRoomId} closed by bot. Reason: ${reason}. Emitted event.`);
    return chatRoom;
}

// Method for N8N to mark a chat as resolved (status update, might not mean 'closed')
async markChatResolved(chatRoomId: string, reason: string): Promise<ChatRoom> {
    this.logger.log(`Marking chat room ${chatRoomId} as resolved by bot. Reason: ${reason}`);
    const chatRoom = await this.chatRoomRepository.findOneBy({ id: chatRoomId });
    if (!chatRoom) throw new HttpException('Chat room not found for marking resolved', HttpStatus.NOT_FOUND);

    // Example: update a custom status or add a tag.
    // For now, let's assume 'resolved' is a specific type of 'closed' or a sub-status.
    // This depends on how 'resolved' is defined in your system.
    // If 'resolved' means 'closed', use similar logic to closeChatRoomByBot.
    // If it's a different status, update accordingly.
    // For this example, let's treat it as a closure with a specific reason.
    chatRoom.status = 'closed'; // Or 'resolved' if that's a distinct status
    chatRoom.current_handler = 'system';
    chatRoom.closed_at = new Date();
    chatRoom.closure_reason = `Resolved by bot: ${reason}`;
    await this.chatRoomRepository.save(chatRoom);

    this.chatGateway.emitToRoom(chatRoomId, 'chat_resolved', { chatRoomId, reason });
    this.logger.log(`Chat room ${chatRoomId} marked as resolved by bot. Emitted event.`);
    return chatRoom;
}

// Placeholder for getChatRoom, adapt as needed
async getChatRoom(chatRoomId: string): Promise<ChatRoom | null> {
    return this.chatRoomRepository.findOneBy({ id: chatRoomId });
}

// Placeholder for closeChatRoom, adapt as needed for different closers
async closeChatRoom(chatRoomId: string, closedById: string, reason?: string): Promise<ChatRoom> {
    // This method needs to exist and handle closure logic, including setting status, closed_by_id etc.
    // For now, this is a simplified placeholder.
    const chatRoom = await this.getChatRoom(chatRoomId);
    if(chatRoom) {
        chatRoom.status = 'closed';
        // chatRoom.closed_by_id = closedById;
        chatRoom.closure_reason = reason;
        chatRoom.closed_at = new Date();
        return this.chatRoomRepository.save(chatRoom);
    }
    throw new HttpException('Chat room not found for closure', HttpStatus.NOT_FOUND);
}

```

### 3.2.2 Operator Entity Enhancements

**File Location**: `backend/src/modules/chat/entities/operator.entity.ts` (or similar)

To support robust operator availability checks (`getAvailableOperators()` in `ChatService`), the `Operator` entity should include fields like:

```typescript
// Example fields for Operator Entity
// @Column({ default: false })
// is_online: boolean; // Tracks if operator has an active connection/session

// @Column({ type: 'varchar', length: 50, default: 'offline' }) // e.g., 'available', 'busy', 'offline'
// status: string;

// @Column({ type: 'int', default: 0 })
// current_assigned_chats: number; // Number of active chats currently assigned

// @Column({ type: 'int', default: 5 }) // Configurable maximum concurrent chats
// max_concurrent_chats: number;

// @Column({ type: 'timestamp with time zone', nullable: true })
// last_activity_at: Date; // Updated on any significant operator action
```

**Action**: Review the existing `Operator` entity and add/update these fields as necessary. Ensure services that manage operator login/logout and status changes update these fields correctly.

### 3.3 Phase 3: Incoming N8N API Endpoints

This phase details the API endpoints that N8N will call on the NestJS backend.

#### 3.3.1 N8N Webhook Controller

**File Location**: `backend/src/modules/n8n-integration/controllers/n8n-webhook.controller.ts`
**Module**: `N8nIntegrationModule` (see [Phase 4](#n8nintegrationmodule)).
**Purpose**: Provides HTTP endpoints for N8N to send data (e.g., bot messages, actions) back to the NestJS application. All endpoints under this controller MUST be protected by the `N8nAuthGuard`.

```typescript
import {
  Controller,
  Post,
  Body,
  Headers,
  HttpException,
  HttpStatus,
  Logger,
  UseGuards,
  RawBodyRequest,
} from '@nestjs/common';
import { N8nIntegrationService } from '../n8n-integration.service';
import { ChatService } from '../../chat/services/chat.service'; // Adjust path as needed
import { N8nAuthGuard } from '../guards/n8n-auth.guard'; // To be created
import { ConfigService } from '@nestjs/config'; // For accessing raw body setting

// DTO for N8N sending a message to a chat room
export interface N8nBotMessageDto {
  chatRoomId: string; // Target chat room
  content: string; // Message content (plain text)
  messageType?: 'text' | 'quick_reply' | 'card' | string; // Type of message, defaults to 'text'
  metadata?: {
    // For rich messages
    quickReplies?: Array<{ title: string; payload: string }>;
    cards?: Array<{
      title: string;
      subtitle?: string;
      imageUrl?: string;
      buttons?: Array<{
        title: string;
        type: 'postback' | 'web_url';
        payload?: string;
        url?: string;
      }>;
    }>;
    // Other metadata N8N might send
  };
  // Optional actions N8N might want NestJS to perform after sending the message
  actions?: Array<{
    type: 'transfer_to_operator' | 'close_chat' | 'request_feedback' | string; // Extensible
    data?: Record<string, any>; // Action-specific data
  }>;
}

// DTO for N8N triggering a specific action on a chat room
export interface N8nChatActionDto {
  chatRoomId: string;
  action: 'close_chat' | 'transfer_to_operator' | 'mark_resolved' | string; // Extensible
  reason?: string; // Reason for the action
  metadata?: Record<string, any>; // Additional context for the action
  // Example: for transfer_to_operator, metadata could include preferred_skill, original_query
}

@Controller('api/v1/n8n-hooks') // Versioned API endpoint
@UseGuards(N8nAuthGuard) // Secure all routes in this controller
export class N8nWebhookController {
  private readonly logger = new Logger(N8nWebhookController.name);

  constructor(
    // N8nIntegrationService is primarily for outgoing, but might have utils needed here
    // private n8nIntegrationService: N8nIntegrationService,
    private chatService: ChatService,
    private configService: ConfigService,
  ) {}

  /**
   * Endpoint for N8N to send a message from the bot to a specific chat room.
   */
  @Post('bot-message')
  async handleBotMessageFromN8n(
    @Body() messageDto: N8nBotMessageDto,
  ): Promise<any> {
    this.logger.log(
      `Received bot message from N8N for chatRoomId: ${messageDto.chatRoomId}. Content: "${messageDto.content.substring(0, 50)}..."`,
    );
    try {
      const chatRoom = await this.chatService.getChatRoom(
        messageDto.chatRoomId,
      );
      if (!chatRoom) {
        this.logger.warn(
          `Chat room ${messageDto.chatRoomId} not found for N8N bot message.`,
        );
        throw new HttpException('Chat room not found', HttpStatus.NOT_FOUND);
      }

      // It's crucial that chatService.sendBotMessage correctly identifies the sender as 'bot'
      // and handles encryption if necessary.
      const sentMessage = await this.chatService.sendBotMessage({
        chat_room_id: messageDto.chatRoomId,
        content: messageDto.content,
        message_type: messageDto.messageType || 'text',
        metadata: messageDto.metadata,
      });
      this.logger.log(
        `Bot message ${sentMessage.id} successfully processed and sent to room ${messageDto.chatRoomId}.`,
      );

      // Process any subsequent actions requested by N8N in the same payload
      if (messageDto.actions && messageDto.actions.length > 0) {
        this.logger.log(
          `Processing ${messageDto.actions.length} actions from N8N for chat ${messageDto.chatRoomId}.`,
        );
        await this.processN8nActions(
          messageDto.chatRoomId,
          'system_n8n_bot_message',
          messageDto.actions,
        );
      }

      return {
        success: true,
        messageId: sentMessage.id,
        timestamp: sentMessage.created_at,
      };
    } catch (error) {
      this.logger.error(
        `Error handling bot message from N8N for chat ${messageDto.chatRoomId}: ${error.message}`,
        error.stack,
      );
      // Avoid re-throwing generic HttpException if error is already one
      if (error instanceof HttpException) throw error;
      throw new HttpException(
        'Failed to process bot message',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Endpoint for N8N to trigger specific actions on a chat room (e.g., close chat, transfer to operator).
   */
  @Post('chat-action')
  async handleChatActionFromN8n(
    @Body() actionDto: N8nChatActionDto,
  ): Promise<any> {
    this.logger.log(
      `Received chat action from N8N: ${actionDto.action} for chatRoomId: ${actionDto.chatRoomId}. Reason: ${actionDto.reason || 'N/A'}`,
    );
    try {
      await this.processN8nActions(actionDto.chatRoomId, 'system_n8n_action', [
        actionDto,
      ]); // Wrap single action in array
      return {
        success: true,
        action: actionDto.action,
        chatRoomId: actionDto.chatRoomId,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `Error handling chat action from N8N for chat ${actionDto.chatRoomId}, action ${actionDto.action}: ${error.message}`,
        error.stack,
      );
      if (error instanceof HttpException) throw error;
      throw new HttpException(
        'Failed to process chat action',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Centralized processing of actions requested by N8N.
   */
  private async processN8nActions(
    chatRoomId: string,
    initiatorId: string, // e.g., 'system_n8n_bot_message', 'system_n8n_action'
    actions: Array<{
      type: string;
      data?: Record<string, any>;
      reason?: string;
    }>,
  ): Promise<void> {
    for (const action of actions) {
      this.logger.log(
        `Processing N8N action: ${action.type} for chatRoomId: ${chatRoomId}`,
      );
      switch (action.type) {
        case 'transfer_to_operator':
          // The 'reason' for transfer can come from action.data.reason or action.reason
          const transferReason =
            action.data?.reason ||
            action.reason ||
            'Transfer requested by N8N bot';
          const transferResult =
            await this.chatService.requestOperatorEscalation(
              chatRoomId,
              initiatorId,
              transferReason,
            );
          if (!transferResult.success) {
            this.logger.warn(
              `N8N requested operator transfer for chat ${chatRoomId}, but it failed: ${transferResult.message}`,
            );
            // Optionally, notify N8N back if the transfer failed (e.g., via another webhook call if critical)
          }
          break;
        case 'close_chat':
          const closeReason =
            action.data?.reason || action.reason || 'Chat closed by N8N bot';
          await this.chatService.closeChatRoomByBot(chatRoomId, closeReason); // Using specific method for bot closure
          break;
        case 'mark_resolved':
          const resolvedReason =
            action.data?.reason ||
            action.reason ||
            'Chat marked as resolved by N8N bot';
          await this.chatService.markChatResolved(chatRoomId, resolvedReason);
          break;
        // Add cases for 'request_feedback' or other custom actions if needed
        default:
          this.logger.warn(
            `Received unknown N8N action type: ${action.type} for chat ${chatRoomId}.`,
          );
          // Optionally, throw an error or log more details
          // throw new HttpException(`Unsupported N8N action type: ${action.type}`, HttpStatus.BAD_REQUEST);
          break;
      }
    }
  }

  /**
   * Health check endpoint for N8N to verify connectivity.
   * This endpoint should also be protected by N8nAuthGuard if it's not meant for public probing.
   * If N8N uses a simple GET for health checks without signature, this might need a separate, unguarded route or guard modification.
   * For POST with signature, it's fine.
   */
  @Post('health-check') // Changed to POST to align with other signed endpoints
  async n8nHealthCheck(): Promise<any> {
    this.logger.log('N8N health check endpoint called.');
    return {
      status: 'NestJS N8N Integration Endpoint is Healthy',
      timestamp: new Date().toISOString(),
    };
  }
}
```

#### 3.3.2 N8N Authentication Guard

**File Location**: `backend/src/modules/n8n-integration/guards/n8n-auth.guard.ts`
**Module**: `N8nIntegrationModule`.
**Purpose**: Secures incoming webhook calls from N8N by validating a signature.

```typescript
// backend/src/modules/n8n-integration/guards/n8n-auth.guard.ts
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  HttpException,
  HttpStatus,
  RawBodyRequest,
} from '@nestjs/common';
import { N8nIntegrationService } from '../n8n-integration.service'; // For validateIncomingWebhookSignature
import { ConfigService } from '@nestjs/config';
import { Observable } from 'rxjs'; // Required for CanActivate interface

@Injectable()
export class N8nAuthGuard implements CanActivate {
  private readonly logger = new Logger(N8nAuthGuard.name);
  private readonly n8nIncomingWebhookSecret: string | undefined;
  private readonly isN8nIntegrationEnabled: boolean;

  constructor(
    private n8nIntegrationService: N8nIntegrationService, // Used for its validation method
    private configService: ConfigService,
  ) {
    this.n8nIncomingWebhookSecret = this.configService.get<string>(
      'N8N_INCOMING_WEBHOOK_SECRET',
    );
    this.isN8nIntegrationEnabled = this.configService.get<boolean>(
      'ENABLE_N8N_INTEGRATION',
      false,
    );

    if (this.isN8nIntegrationEnabled && !this.n8nIncomingWebhookSecret) {
      this.logger.warn(
        'N8N_INCOMING_WEBHOOK_SECRET is not set while N8N integration is enabled. ' +
          'Incoming webhooks from N8N cannot be securely validated. THIS IS A SECURITY RISK.',
      );
    }
  }

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    if (!this.isN8nIntegrationEnabled) {
      this.logger.warn(
        'N8NAuthGuard: N8N integration is disabled. Denying access to N8N webhook endpoint.',
      );
      // Or, allow but with a clear log that it's only because N8N is off.
      // For security, better to deny if the guard is applied and N8N is off.
      throw new HttpException(
        'N8N Integration is disabled.',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }

    if (!this.n8nIncomingWebhookSecret) {
      this.logger.warn(
        'N8NAuthGuard: N8N_INCOMING_WEBHOOK_SECRET is not configured. ' +
          'Allowing request without signature validation (INSECURE). Endpoint should be protected otherwise.',
      );
      return true; // Allow if secret not set, but this is a security hole.
      // Consider `false` for production if secret is mandatory.
    }

    const request = context.switchToHttp().getRequest<RawBodyRequest<any>>(); // Ensure RawBodyRequest is used
    const signatureFromHeader =
      request.headers['x-n8n-signature'] ||
      request.headers['x-webhook-signature']; // Use a consistent header name

    if (!signatureFromHeader) {
      this.logger.warn(
        'N8NAuthGuard: Missing webhook signature header (e.g., x-n8n-signature) from incoming N8N request.',
      );
      throw new HttpException('Missing signature', HttpStatus.UNAUTHORIZED);
    }

    // Accessing rawBody. This requires specific setup in main.ts:
    // app.use(bodyParser.json({ verify: (req: any, res, buf) => { req.rawBody = buf; } }));
    // OR for NestJS v8+ with Fastify:
    // The `rawBody` might be available via `request.raw`. Check NestJS docs for current best practice.
    // For Express (default):
    // In main.ts:
    // const app = await NestFactory.create(AppModule, { bodyParser: false }); // Disable global body parser
    // app.use(express.raw({ type: 'application/json', verify: (req: any, res, buf) => { req.rawBody = buf; } }));
    // app.use(express.json({ verify: (req: any, res, buf) => { req.rawBody = buf; } })); // Then re-apply json parser also with verify
    // Or more simply if NestJS `json` parser supports `rawBody` option directly in `NestFactory.create`.
    // As of NestJS 9+, you can pass `rawBody: true` to the `json` middleware options.
    // e.g. `app.use(express.json({ rawBody: true }));`
    // The AI implementing this needs to ensure `request.rawBody` is populated.

    const rawBody = request.rawBody;
    if (!rawBody) {
      this.logger.error(
        'N8NAuthGuard: Raw request body not available for N8N signature validation. ' +
          'Ensure `rawBody: true` is set for the JSON body parser in `main.ts` or that `bodyParser` is configured to expose it.',
      );
      throw new HttpException(
        'Internal server error during N8N authentication (raw body missing)',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const payloadString = rawBody.toString('utf8'); // Ensure correct encoding
    const isValid = this.n8nIntegrationService.validateIncomingWebhookSignature(
      payloadString,
      signatureFromHeader as string,
    );

    if (!isValid) {
      this.logger.warn('N8NAuthGuard: Invalid N8N webhook signature.');
      throw new HttpException('Invalid signature', HttpStatus.FORBIDDEN); // Use 403 for invalid signature
    }

    this.logger.debug(
      'N8NAuthGuard: Valid N8N webhook signature. Request allowed.',
    );
    return true;
  }
}
```

**Note on Raw Body for `N8nAuthGuard`**:
The implementing AI must ensure that the raw request body is available to the `N8nAuthGuard`. This typically involves configuring the body parser in `main.ts`.
Example for NestJS with Express (add to `main.ts`):

```typescript
// import * as express from 'express'; // if not already imported
// async function bootstrap() {
//   const app = await NestFactory.create(AppModule, {
//     // If using NestJS built-in JSON parser, it might have an option for rawBody
//   });
//   // For Express, ensure rawBody is captured.
//   // This needs to be done BEFORE NestJS's own global pipes or interceptors might consume the body.
//   // One common way:
//   app.use(express.json({
//     verify: (req: any, res, buf) => {
//       req.rawBody = buf;
//     }
//   }));
//   // Or if using `bodyParser: false` in NestFactory.create and then adding middleware:
//   // app.use(bodyParser.json({ verify: (req: any, res, buf) => { req.rawBody = buf } }));

//   // A simpler way for NestJS v9+ might be:
//   // app.useBodyParser('json', { rawBody: true }); // Check exact API
//   // Or in main.ts:
//   // app.use(json({ verify: (req: any, res, buf) => { req.rawBody = buf.toString() }}));
//   // The key is `req.rawBody = buf;`
//   await app.listen(3000);
// }
// bootstrap();
```

The AI should use the appropriate method for the NestJS version and underlying HTTP server (Express/Fastify).

### 3.4 Phase 4: Module Configuration

This section details how the new services and controllers are organized into NestJS modules and how these modules are integrated.

#### 3.4.1 CommonAppModule

**File Location**: `backend/src/common/common-app.module.ts` (Create if not exists)
**Purpose**: Houses common services like `OfficeHoursService`.

```typescript
// backend/src/common/common-app.module.ts
import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config'; // OfficeHoursService uses ConfigService
import { OfficeHoursService } from './services/office-hours.service';
// Import HttpModule if OfficeHoursService makes HTTP calls for holidays
// import { HttpModule } from '@nestjs/axios';

@Global() // Make services available globally without importing CommonAppModule everywhere
@Module({
  imports: [
    ConfigModule,
    // HttpModule.register({ timeout: 5000, maxRedirects: 5 }), // If holiday API is used
  ],
  providers: [OfficeHoursService],
  exports: [OfficeHoursService],
})
export class CommonAppModule {}
```

#### 3.4.2 N8nIntegrationModule

**File Location**: `backend/src/modules/n8n-integration/n8n-integration.module.ts`
**Purpose**: Encapsulates all N8N-specific components.

```typescript
// backend/src/modules/n8n-integration/n8n-integration.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios'; // For N8nIntegrationService outgoing calls
import { N8nIntegrationService } from './n8n-integration.service';
import { N8nWebhookController } from './controllers/n8n-webhook.controller';
import { N8nAuthGuard } from './guards/n8n-auth.guard';
import { ChatModule } from '../chat/chat.module'; // ForwardRef if circular dependency with ChatService

@Module({
  imports: [
    ConfigModule,
    HttpModule.register({
      // Configure HttpModule for N8nIntegrationService
      timeout: 10000, // Default timeout for N8N calls
      maxRedirects: 3,
    }),
    // ForwardRef if ChatService injects N8nIntegrationService and vice-versa,
    // or if N8nWebhookController needs ChatService which is in ChatModule.
    // For now, assume N8nWebhookController needs ChatService, so ChatModule might be needed.
    // Consider a more decoupled approach if ChatService is not directly needed by controller.
    // If ChatService is needed, ensure ChatModule exports ChatService.
    // For simplicity, if ChatService is only used by N8nWebhookController, it's okay.
    // However, if N8nIntegrationService needs ChatService, that's a circular dep.
    // Let's assume ChatService is provided by ChatModule and N8nWebhookController uses it.
    ChatModule, // This implies ChatModule exports ChatService
  ],
  controllers: [N8nWebhookController],
  providers: [N8nIntegrationService, N8nAuthGuard], // N8nAuthGuard needs N8nIntegrationService
  exports: [N8nIntegrationService], // Export if other modules need to call N8N directly
})
export class N8nIntegrationModule {}
```

**Note on `ChatModule` import**: If `N8nWebhookController` uses `ChatService`, `ChatModule` must be imported here, and `ChatModule` must export `ChatService`. This is a common pattern.

#### 3.4.3 ChatModule Updates

**File Location**: `backend/src/modules/chat/chat.module.ts`
**Purpose**: Integrate `OfficeHoursService` and `N8nIntegrationService` into the chat logic.

```typescript
// backend/src/modules/chat/chat.module.ts
import { Module, forwardRef } from '@nestjs/common';
// ... other existing imports for TypeOrmModule, entities, controllers, services ...
import { OfficeHoursService } from '../../common/services/office-hours.service'; // If CommonAppModule is not Global
import { N8nIntegrationModule } from '../n8n-integration/n8n-integration.module'; // To get N8nIntegrationService
import { N8nIntegrationService } from '../n8n-integration/n8n-integration.service';
// ChatService, ChatGateway, EncryptionService, ChatAuthController, ChatOperatorController etc.

@Module({
  imports: [
    // TypeOrmModule.forFeature([...]),
    // ConfigModule, // If ChatService uses ConfigService directly
    // AuthModule, // For JWT strategy if used by Chat guards
    N8nIntegrationModule, // Provides N8nIntegrationService
    // CommonAppModule, // Not needed if CommonAppModule is Global
  ],
  controllers: [
    /* ChatController, ChatOperatorController */
  ],
  providers: [
    ChatService,
    // ChatAuthService,
    // EncryptionService,
    // ChatGateway,
    // OfficeHoursService, // Provided globally by CommonAppModule if @Global
    // N8nIntegrationService, // Provided by N8nIntegrationModule
    // Ensure all existing providers are listed
  ],
  exports: [
    ChatService /*, other services if needed by other modules like N8nIntegrationModule */,
  ],
})
export class ChatModule {}
```

**Important**:

- If `CommonAppModule` is `@Global()`, `OfficeHoursService` is available everywhere.
- `N8nIntegrationService` is imported from `N8nIntegrationModule`. `ChatService` will inject `N8nIntegrationService`.
- `ChatModule` needs to export `ChatService` if `N8nIntegrationModule` (specifically `N8nWebhookController`) imports `ChatModule` to use `ChatService`.

#### 3.4.4 AppModule Updates

**File Location**: `backend/src/app.module.ts`
**Purpose**: Import new top-level modules.

```typescript
// backend/src/app.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
// ... other core module imports (TypeOrmModule, etc.)
import { CommonAppModule } from './common/common-app.module'; // If not already there
import { N8nIntegrationModule } from './modules/n8n-integration/n8n-integration.module';
import { ChatModule } from './modules/chat/chat.module'; // Assuming it's a feature module
// ... other feature modules

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true, envFilePath: '.env' }),
    // TypeOrmModule.forRootAsync({ ... }),
    CommonAppModule, // For OfficeHoursService etc.
    ChatModule, // Existing chat module
    N8nIntegrationModule, // New N8N module
    // ... other modules
  ],
  // ... controllers, providers for AppModule
})
export class AppModule {}
```

### 3.5 Phase 5: Testing and Finalization

#### 3.5.1 Testing Considerations

- **`OfficeHoursService`**:
  - Unit test `isWithinOfficeHours` with various dates, times, and timezones.
  - Unit test `isHoliday` with mock API responses (success, failure) and fallback to local list.
  - Unit test `shouldRouteToOperator` covering all branches (office hours, outside, holiday).
- **`N8nIntegrationService`**:
  - Unit test `sendChatEvent` with mock `HttpService` to verify payload, headers, and handling of success/error responses from N8N.
  - Test behavior when N8N is disabled (`isN8nEnabled: false`).
  - Unit test `validateIncomingWebhookSignature` with correct and incorrect signatures.
- **`ChatService`**:
  - Integration test `initiateChatSession`:
    - Scenario: N8N enabled, during office hours, no holiday, operators available (routes to pending_assignment, N8N not called initially).
    - Scenario: N8N enabled, outside office hours (routes to bot, N8N `handleNewChat` called).
    - Scenario: N8N enabled, holiday (routes to bot, N8N `handleNewChat` called).
    - Scenario: N8N enabled, office hours, no holiday, no operators (routes to bot, N8N `handleNewChat` called).
    - Scenario: N8N disabled, outside office hours (routes to 'offline_pending' or similar, N8N not called, system message sent).
    - Scenario: N8N disabled, office hours, no operators (routes to 'queued_for_operator', N8N not called, system message sent).
  - Integration test `sendMessage`:
    - User message in bot-handled chat (N8N `handleNewMessage` called).
    - User message in bot-handled chat, N8N disabled/fails (logs warning, chat continues).
  - Integration test `requestOperatorEscalation`:
    - Successful escalation from bot to operator.
    - Escalation attempt when no operators available.
    - Escalation attempt when chat not bot-handled.
- **`N8nWebhookController` / `N8nAuthGuard`**:
  - E2E test incoming calls from a mock N8N:
    - With valid signature.
    - With invalid signature (expect 403).
    - Without signature (expect 401).
    - Test `handleBotMessageFromN8n` and `handleChatActionFromN8n` endpoints with valid/invalid DTOs.
    - Test raw body parsing for signature validation.
- **Overall Flow**:
  - E2E test the entire chat initiation and escalation flow with a mock N8N service.

#### 3.5.2 Deployment Notes

- Ensure all new environment variables are set in production/staging environments.
- Configure N8N workflows to call the correct NestJS `/api/v1/n8n-hooks/...` endpoints and include the `X-N8N-Signature` header using the `N8N_INCOMING_WEBHOOK_SECRET`.
- Monitor logs closely after deployment for any issues in routing or N8N communication.

### 3.2 Phase 2: Enhanced Chat Service Integration

#### 3.2.1 Updated Chat Service

**File Location**: `backend/src/modules/chat/services/chat.service.ts`
**Module**: `ChatModule` (see [Phase 4](#chatmodule-updates)).
**Key Changes**: Integrate `OfficeHoursService` and `N8nIntegrationService` for routing decisions and event notifications. Gracefully handle disabled N8N integration.

```typescript
// Add these imports at the top (adjust paths as necessary)
import { N8nIntegrationService, N8nBotResponse } from '../../n8n-integration/n8n-integration.service';
// ... other existing imports like Repositories, DTOs, Entities, Gateway, EncryptionService

// Add to constructor's parameters:
// private readonly officeHoursService: OfficeHoursService,
// private readonly n8nIntegrationService: N8nIntegrationService,

// Ensure these are correctly provided in ChatModule.

// Enhanced initiateChatSession method
async initiateChatSession(
  initiateDto: InitiateChatDto,
  userAgent?: string,
  ipAddress?: string,
): Promise<{ chatRoom: ChatRoom; user: ChatUser; initialHandler: string; n8nNotified: boolean }> {
  this.logger.log(`Initiating chat session for user: ${initiateDto.name}`);
  try {
    const user = await this.createOrUpdateUser(initiateDto, userAgent, ipAddress);
    const routingDecision = await this.determineInitialRouting(new Date());
    let finalHandler = routingDecision.handler;
    let n8nNotified = false;

    const chatRoomEntity = this.chatRoomRepository.create({
      type: 'user-operator', // Standard type for user-initiated chats
      status: 'active',      // Initial status
      current_handler: finalHandler, // Determined by routing logic
      handler_assigned_at: new Date(),
      created_at: new Date(),
      updated_at: new Date(),
      // Any other necessary fields from initiateDto or defaults
    });
    const savedChatRoom = await this.chatRoomRepository.save(chatRoomEntity);
    this.logger.log(`Chat room ${savedChatRoom.id} created. Initial handler: ${finalHandler}, Reason: ${routingDecision.reason}`);

    const participant = this.participantRepository.create({
      chat_room_id: savedChatRoom.id,
      user_id: user.id,
      role: 'user',
      joined_at: new Date(),
    });
    await this.participantRepository.save(participant);
    this.logger.log(`User ${user.id} added as participant to chat room ${savedChatRoom.id}`);

    if (routingDecision.routeToBot) {
      const n8nResponse = await this.n8nIntegrationService.handleNewChat(
        savedChatRoom.id,
        user.id,
        initiateDto.initial_message, // Send initial message if available
      );
      n8nNotified = n8nResponse.success;

      if (!n8nResponse.success) {
        this.logger.warn(`N8N new_chat event failed for ${savedChatRoom.id}. Reason: ${n8nResponse.reason}. Chat continues with handler: ${finalHandler}.`);
        // If N8N is explicitly disabled or URL not configured, and routing said 'bot',
        // we need to adjust the handler to a non-bot state.
        if (n8nResponse.reason === 'N8N_DISABLED' || n8nResponse.reason === 'N8N_WEBHOOK_URL_NOT_CONFIGURED') {
          if (routingDecision.reason === 'No operators available' || routingDecision.reason.startsWith('Holiday')) {
            finalHandler = 'queued_for_operator'; // Or a more specific state like 'holiday_queue'
            await this.sendSystemMessage(savedChatRoom.id, "Thank you for reaching out. All our operators are currently busy, or it's a holiday. You've been placed in a queue.");
          } else if (routingDecision.reason === 'Outside office hours') {
            finalHandler = 'offline_pending';
            await this.sendSystemMessage(savedChatRoom.id, "Thank you for reaching out. We are currently outside office hours. Please try again later or leave a message.");
          } else {
            // Default fallback if bot was intended but N8N is off for other reasons
            finalHandler = 'pending_assignment';
          }
          await this.chatRoomRepository.update(savedChatRoom.id, { current_handler: finalHandler, handler_assigned_at: new Date() });
          this.logger.log(`Chat room ${savedChatRoom.id} handler updated to ${finalHandler} due to N8N being unavailable.`);
        }
      } else {
         this.logger.log(`N8N successfully notified for new chat ${savedChatRoom.id}.`);
      }
    } else {
        // Routed to operator/pending_assignment directly
        this.logger.log(`Chat ${savedChatRoom.id} routed directly to ${finalHandler}. No N8N notification needed at this stage.`);
        // Potentially notify operators about a new pending chat.
    }

    return {
      chatRoom: await this.chatRoomRepository.findOneBy({ id: savedChatRoom.id }), // Re-fetch to get updated handler
      user,
      initialHandler: finalHandler,
      n8nNotified,
    };
  } catch (error) {
    this.logger.error(`Error initiating chat session for ${initiateDto.name}: ${error.message}`, error.stack);
    throw new HttpException('Failed to initiate chat session', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

// New method for routing determination
private async determineInitialRouting(currentDate: Date): Promise<{
  handler: 'bot' | 'pending_assignment' | 'queued_for_operator' | 'offline_pending'; // More specific handler states
  routeToBot: boolean; // True if the ideal initial handler is the bot
  reason: string;
  holidayDetails?: HolidayCheck;
}> {
  this.logger.debug(`Determining initial routing for date: ${currentDate.toISOString()}`);
  try {
    const officeHoursCheck = await this.officeHoursService.shouldRouteToOperator(currentDate);

    if (!officeHoursCheck.routeToOperator) { // Outside office hours or Holiday
      const reason = officeHoursCheck.reason;
      this.logger.log(`Initial routing: to Bot. Reason: ${reason}`);
      return {
        handler: 'bot', // Ideal handler is bot
        routeToBot: true,
        reason: reason,
        holidayDetails: officeHoursCheck.details,
      };
    }

    // Within office hours and not a holiday, check operator availability
    const availableOperators = await this.getAvailableOperators();
    if (availableOperators.length === 0) {
      const reason = 'No operators available during office hours';
      this.logger.log(`Initial routing: to Bot. Reason: ${reason}`);
      return {
        handler: 'bot', // Ideal handler is bot (as operators are busy)
        routeToBot: true,
        reason: reason,
      };
    }

    const reason = 'Operators available during office hours';
    this.logger.log(`Initial routing: to Operator (pending assignment). Reason: ${reason}`);
    return {
      handler: 'pending_assignment', // Assign to operator queue
      routeToBot: false,
      reason: reason,
    };
  } catch (error) {
    this.logger.error(`Error in determineInitialRouting: ${error.message}`, error.stack);
    // Default to bot on error, to ensure chat can still be captured by N8N if configured
    return {
      handler: 'bot',
      routeToBot: true,
      reason: 'Error in routing logic - defaulting to bot handling',
    };
  }
}

// Enhanced sendMessage method
async sendMessage(sendMessageDto: SendMessageDto, senderId: string, senderType: 'user' | 'operator'): Promise<Message> {
  this.logger.debug(`sendMessage called by ${senderType} ${senderId} for room ${sendMessageDto.chat_room_id}`);
  try {
    const chatRoom = await this.chatRoomRepository.findOne({ where: { id: sendMessageDto.chat_room_id }});
    if (!chatRoom) throw new HttpException('Chat room not found', HttpStatus.NOT_FOUND);

    // Decrypt content before sending to N8N if it was encrypted for storage by user/operator
    // For this example, assuming sendMessageDto.content is plain text from client,
    // and encryption happens before DB save in createMessage.
    // If message.content is already encrypted, it needs decryption before sending to N8N.
    // Let's assume createMessage returns the message with plain text content for now for N8N.
    const message = await this.createMessage(sendMessageDto, senderId, senderType); // This should handle encryption before saving

    // If chat is currently handled by 'bot' and the sender is a 'user', forward to N8N
    if (chatRoom.current_handler === 'bot' && senderType === 'user') {
      this.logger.log(`Chat room ${chatRoom.id} is bot-handled. Forwarding user message ${message.id} to N8N.`);
      // Ensure message.content sent to N8N is plain text.
      // If createMessage encrypts, you need the plain text version here.
      // Assuming sendMessageDto.content is the plain text to be sent.
      const n8nResponse = await this.n8nIntegrationService.handleNewMessage(
        chatRoom.id,
        message.id, // Local DB message ID
        sendMessageDto.content, // Plain text content
        senderId,
        senderType,
        message.created_at.toISOString() // Original message timestamp
      );
      if (!n8nResponse.success) {
        this.logger.warn(`N8N handleNewMessage event failed for chat ${chatRoom.id}, message ${message.id}. Reason: ${n8nResponse.reason}. Bot may not respond.`);
        // Optionally, inform the user about the bot's temporary unavailability if N8N is configured but failing.
        if (n8nResponse.reason !== 'N8N_DISABLED' && n8nResponse.reason !== 'N8N_WEBHOOK_URL_NOT_CONFIGURED') {
            // await this.sendSystemMessage(chatRoom.id, "I'm having trouble reaching my AI assistant at the moment. Please wait, or an operator will assist you if available.");
        }
      }
    }

    await this.chatRoomRepository.update(chatRoom.id, { last_message_at: new Date(), updated_at: new Date() });

    this.chatGateway.emitToRoom(chatRoom.id, 'new_message', {
      message: this.formatMessageResponse(message), // formatMessageResponse should handle decrypting for client if needed
      chatRoomId: chatRoom.id,
    });
    this.logger.log(`Message ${message.id} sent and emitted to room ${chatRoom.id}`);
    return message;
  } catch (error) {
    this.logger.error(`Error sending message in room ${sendMessageDto.chat_room_id}: ${error.message}`, error.stack);
    throw error; // Re-throw original error or a classified HttpException
  }
}

// New method for handling escalation requests from a user in a bot-managed chat
async requestOperatorEscalation(chatRoomId: string, userId: string, reasonMessage?: string): Promise<{
  success: boolean;
  message: string; // User-facing message
  operatorAssigned: boolean;
  assignedOperatorId?: string;
}> {
  this.logger.log(`Operator escalation requested by user ${userId} for chat room ${chatRoomId}. Reason: ${reasonMessage || 'N/A'}`);
  try {
    const chatRoom = await this.chatRoomRepository.findOneBy({ id: chatRoomId });
    if (!chatRoom) throw new HttpException('Chat room not found', HttpStatus.NOT_FOUND);

    if (chatRoom.current_handler !== 'bot') {
      this.logger.warn(`Escalation request for chat ${chatRoomId} which is not bot-handled (current: ${chatRoom.current_handler}).`);
      return { success: false, message: 'This chat is already being handled or pending an operator.', operatorAssigned: chatRoom.current_handler === 'operator' };
    }

    // Notify N8N that user is requesting escalation (optional, but good for N8N to know)
    await this.n8nIntegrationService.handleUserEscalationRequest(chatRoomId, userId, reasonMessage);

    const availableOperators = await this.getAvailableOperators();
    if (availableOperators.length === 0) {
      this.logger.log(`No operators available for escalation in chat ${chatRoomId}. Informing user.`);
      const noOperatorMsg = 'Maaf, saat ini tidak ada operator yang tersedia. Anda dapat melanjutkan percakapan dengan chatbot atau mencoba lagi nanti.';
      await this.sendSystemMessage(chatRoomId, noOperatorMsg);
      return { success: false, message: noOperatorMsg, operatorAssigned: false };
    }

    const assignedOperator = availableOperators[0]; // TODO: Implement better assignment logic (e.g., round-robin, least busy)
    this.logger.log(`Assigning operator ${assignedOperator.id} to chat ${chatRoomId} due to user escalation.`);

    await this.chatRoomRepository.update(chatRoomId, {
      current_handler: 'operator', // Explicitly set to 'operator'
      handler_assigned_at: new Date(),
      updated_at: new Date(),
    });

    // Ensure operator is a participant
    let operatorParticipant = await this.participantRepository.findOne({ where: { chat_room_id: chatRoomId, operator_id: assignedOperator.id }});
    if (!operatorParticipant) {
        operatorParticipant = this.participantRepository.create({
            chat_room_id: chatRoomId,
            operator_id: assignedOperator.id,
            role: 'operator',
            joined_at: new Date(),
        });
        await this.participantRepository.save(operatorParticipant);
        this.logger.log(`Operator ${assignedOperator.id} added as participant to chat room ${chatRoomId}.`);
    }


    // Notify N8N about the successful transfer (even if N8N initiated it, this confirms it on NestJS side)
    const n8nTransferResponse = await this.n8nIntegrationService.handleChatTransferToOperator(
      chatRoomId,
      assignedOperator.id,
      reasonMessage || 'User requested human agent',
      userId
    );
    if (!n8nTransferResponse.success) {
        this.logger.warn(`N8N event failed for chat transfer ${chatRoomId}. Transfer to operator proceeds locally. Reason: ${n8nTransferResponse.reason}`);
    }

    const welcomeMsg = `Halo! Saya ${assignedOperator.name}, operator yang akan membantu Anda. Ada yang bisa saya bantu?`;
    await this.sendSystemMessage(chatRoomId, welcomeMsg);

    this.chatGateway.emitToRoom(chatRoomId, 'chat_transferred', {
      chatRoomId,
      handlerType: 'operator',
      operator: { id: assignedOperator.id, name: assignedOperator.name },
      message: welcomeMsg,
    });
    this.logger.log(`Chat ${chatRoomId} successfully transferred to operator ${assignedOperator.id}. User notified.`);

    return { success: true, message: `Anda telah terhubung dengan operator ${assignedOperator.name}.`, operatorAssigned: true, assignedOperatorId: assignedOperator.id };
  } catch (error) {
    this.logger.error(`Error handling operator escalation for chat ${chatRoomId}: ${error.message}`, error.stack);
    throw new HttpException('Failed to process escalation request', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

// Helper method to send system messages (e.g., "Operator joined", "No operators available")
private async sendSystemMessage(chatRoomId: string, content: string): Promise<Message> {
  this.logger.log(`Sending system message to room ${chatRoomId}: "${content}"`);
  // Ensure content is encrypted if your DB stores encrypted messages
  const encryptedContent = await this.encryptionService.encrypt(content);

  const systemMessage = this.messageRepository.create({
    chat_room_id: chatRoomId,
    content: encryptedContent,
    sender_type: 'system', // Or 'bot' if it's a bot-specific system message
    message_type: 'text', // Or a special system message type
    created_at: new Date(),
  });
  const savedMessage = await this.messageRepository.save(systemMessage);

  this.chatGateway.emitToRoom(chatRoomId, 'new_message', {
    message: this.formatMessageResponse(savedMessage), // Ensure this decrypts for client if needed
    chatRoomId,
  });
  return savedMessage;
}

// Helper method to get available operators
// CRITICAL: This logic needs to be robust.
private async getAvailableOperators(): Promise<Operator[]> {
  this.logger.debug('Fetching available operators...');
  // This should query the Operator entity based on:
  // 1. `is_online: true` (boolean, indicates active WebSocket connection or recent dashboard activity).
  // 2. `status: 'available'` (enum/string, operator has set themselves as available).
  // 3. `current_chats < max_chats` (numeric, operator not at capacity).
  // These fields (`is_online`, `status`, `current_chats`, `max_chats`) need to exist on the Operator entity.
  // See section [Operator Entity Enhancements](#operator-entity-enhancements).
  // Order by least busy or longest idle for fairness if multiple operators are available.
  const operators = await this.operatorRepository.find({
    where: {
      status: 'available', // Make sure 'available' is a valid status in Operator entity
      is_online: true,     // Make sure 'is_online' is maintained accurately
      // Add condition for current_chats < max_chats if implemented
    },
    order: {
      // Example: prioritize operators with fewer current chats or longest idle time
      // current_assigned_chats: 'ASC', // Requires a field to track this
      last_activity_at: 'ASC', // Or DESC depending on desired priority
    },
  });
  this.logger.debug(`Found ${operators.length} available operators.`);
  return operators;
}

// Method to allow N8N or system to send a message as 'bot'
async sendBotMessage(dto: {
  chat_room_id: string;
  content: string;
  message_type?: string; // e.g., 'text', 'quick_reply', 'card'
  metadata?: any;
}): Promise<Message> {
  this.logger.log(`Bot sending message to room ${dto.chat_room_id}: "${dto.content.substring(0,50)}..."`);
  const chatRoom = await this.chatRoomRepository.findOneBy({ id: dto.chat_room_id });
  if (!chatRoom) throw new HttpException('Chat room not found for bot message', HttpStatus.NOT_FOUND);

  // Ensure content is encrypted if your DB stores encrypted messages
  const encryptedContent = await this.encryptionService.encrypt(dto.content);

  const botMessage = this.messageRepository.create({
    chat_room_id: dto.chat_room_id,
    content: encryptedContent,
    sender_type: 'bot', // Distinct sender type for bot messages
    // sender_id: null, // Or a dedicated bot user ID if you have one
    message_type: dto.message_type || 'text',
    metadata: dto.metadata, // For rich messages like quick replies, cards
    created_at: new Date(),
  });
  const savedMessage = await this.messageRepository.save(botMessage);

  await this.chatRoomRepository.update(dto.chat_room_id, { last_message_at: new Date(), updated_at: new Date() });

  this.chatGateway.emitToRoom(dto.chat_room_id, 'new_message', {
    message: this.formatMessageResponse(savedMessage), // Ensure this decrypts for client if needed
    chatRoomId: dto.chat_room_id,
  });
  this.logger.log(`Bot message ${savedMessage.id} sent and emitted to room ${dto.chat_room_id}`);
  return savedMessage;
}

// Method for N8N to trigger chat closure
async closeChatRoomByBot(chatRoomId: string, reason: string): Promise<ChatRoom> {
    this.logger.log(`Closing chat room ${chatRoomId} by bot. Reason: ${reason}`);
    const chatRoom = await this.chatRoomRepository.findOneBy({ id: chatRoomId });
    if (!chatRoom) throw new HttpException('Chat room not found for bot closure', HttpStatus.NOT_FOUND);

    if (chatRoom.status === 'closed') {
        this.logger.warn(`Chat room ${chatRoomId} is already closed.`);
        return chatRoom;
    }

    chatRoom.status = 'closed';
    chatRoom.current_handler = 'system'; // Or 'bot_closed'
    chatRoom.closed_at = new Date();
    // chatRoom.closed_by_id = 'SYSTEM_BOT_ID'; // If you have a system/bot ID
    chatRoom.closure_reason = reason;
    await this.chatRoomRepository.save(chatRoom);

    // Notify N8N about the closure (if N8N didn't initiate it)
    // This might be redundant if N8N initiated, but good for consistency.
    await this.n8nIntegrationService.handleChatClosure(chatRoomId, 'bot');

    this.chatGateway.emitToRoom(chatRoomId, 'chat_closed', { chatRoomId, reason });
    this.logger.log(`Chat room ${chatRoomId} closed by bot. Reason: ${reason}. Emitted event.`);
    return chatRoom;
}

// Method for N8N to mark a chat as resolved (status update, might not mean 'closed')
async markChatResolved(chatRoomId: string, reason: string): Promise<ChatRoom> {
    this.logger.log(`Marking chat room ${chatRoomId} as resolved by bot. Reason: ${reason}`);
    const chatRoom = await this.chatRoomRepository.findOneBy({ id: chatRoomId });
    if (!chatRoom) throw new HttpException('Chat room not found for marking resolved', HttpStatus.NOT_FOUND);

    // Example: update a custom status or add a tag.
    // For now, let's assume 'resolved' is a specific type of 'closed' or a sub-status.
    // This depends on how 'resolved' is defined in your system.
    // If 'resolved' means 'closed', use similar logic to closeChatRoomByBot.
    // If it's a different status, update accordingly.
    // For this example, let's treat it as a closure with a specific reason.
    chatRoom.status = 'closed'; // Or 'resolved' if that's a distinct status
    chatRoom.current_handler = 'system';
    chatRoom.closed_at = new Date();
    chatRoom.closure_reason = `Resolved by bot: ${reason}`;
    await this.chatRoomRepository.save(chatRoom);

    this.chatGateway.emitToRoom(chatRoomId, 'chat_resolved', { chatRoomId, reason });
    this.logger.log(`Chat room ${chatRoomId} marked as resolved by bot. Emitted event.`);
    return chatRoom;
}

// Placeholder for getChatRoom, adapt as needed
async getChatRoom(chatRoomId: string): Promise<ChatRoom | null> {
    return this.chatRoomRepository.findOneBy({ id: chatRoomId });
}

// Placeholder for closeChatRoom, adapt as needed for different closers
async closeChatRoom(chatRoomId: string, closedById: string, reason?: string): Promise<ChatRoom> {
    // This method needs to exist and handle closure logic, including setting status, closed_by_id etc.
    // For now, this is a simplified placeholder.
    const chatRoom = await this.getChatRoom(chatRoomId);
    if(chatRoom) {
        chatRoom.status = 'closed';
        // chatRoom.closed_by_id = closedById;
        chatRoom.closure_reason = reason;
        chatRoom.closed_at = new Date();
        return this.chatRoomRepository.save(chatRoom);
    }
    throw new HttpException('Chat room not found for closure', HttpStatus.NOT_FOUND);
}

```

### 3.2.2 Operator Entity Enhancements

**File Location**: `backend/src/modules/chat/entities/operator.entity.ts` (or similar)

To support robust operator availability checks (`getAvailableOperators()` in `ChatService`), the `Operator` entity should include fields like:

```typescript
// Example fields for Operator Entity
// @Column({ default: false })
// is_online: boolean; // Tracks if operator has an active connection/session

// @Column({ type: 'varchar', length: 50, default: 'offline' }) // e.g., 'available', 'busy', 'offline'
// status: string;

// @Column({ type: 'int', default: 0 })
// current_assigned_chats: number; // Number of active chats currently assigned

// @Column({ type: 'int', default: 5 }) // Configurable maximum concurrent chats
// max_concurrent_chats: number;

// @Column({ type: 'timestamp with time zone', nullable: true })
// last_activity_at: Date; // Updated on any significant operator action
```

**Action**: Review the existing `Operator` entity and add/update these fields as necessary. Ensure services that manage operator login/logout and status changes update these fields correctly.

### 3.3 Phase 3: Incoming N8N API Endpoints

This phase details the API endpoints that N8N will call on the NestJS backend.

#### 3.3.1 N8N Webhook Controller

**File Location**: `backend/src/modules/n8n-integration/controllers/n8n-webhook.controller.ts`
**Module**: `N8nIntegrationModule` (see [Phase 4](#n8nintegrationmodule)).
**Purpose**: Provides HTTP endpoints for N8N to send data (e.g., bot messages, actions) back to the NestJS application. All endpoints under this controller MUST be protected by the `N8nAuthGuard`.

```typescript
import {
  Controller,
  Post,
  Body,
  Headers,
  HttpException,
  HttpStatus,
  Logger,
  UseGuards,
  RawBodyRequest,
} from '@nestjs/common';
import { N8nIntegrationService } from '../n8n-integration.service';
import { ChatService } from '../../chat/services/chat.service'; // Adjust path as needed
import { N8nAuthGuard } from '../guards/n8n-auth.guard'; // To be created
import { ConfigService } from '@nestjs/config'; // For accessing raw body setting

// DTO for N8N sending a message to a chat room
export interface N8nBotMessageDto {
  chatRoomId: string; // Target chat room
  content: string; // Message content (plain text)
  messageType?: 'text' | 'quick_reply' | 'card' | string; // Type of message, defaults to 'text'
  metadata?: {
    // For rich messages
    quickReplies?: Array<{ title: string; payload: string }>;
    cards?: Array<{
      title: string;
      subtitle?: string;
      imageUrl?: string;
      buttons?: Array<{
        title: string;
        type: 'postback' | 'web_url';
        payload?: string;
        url?: string;
      }>;
    }>;
    // Other metadata N8N might send
  };
  // Optional actions N8N might want NestJS to perform after sending the message
  actions?: Array<{
    type: 'transfer_to_operator' | 'close_chat' | 'request_feedback' | string; // Extensible
    data?: Record<string, any>; // Action-specific data
  }>;
}

// DTO for N8N triggering a specific action on a chat room
export interface N8nChatActionDto {
  chatRoomId: string;
  action: 'close_chat' | 'transfer_to_operator' | 'mark_resolved' | string; // Extensible
  reason?: string; // Reason for the action
  metadata?: Record<string, any>; // Additional context for the action
  // Example: for transfer_to_operator, metadata could include preferred_skill, original_query
}

@Controller('api/v1/n8n-hooks') // Versioned API endpoint
@UseGuards(N8nAuthGuard) // Secure all routes in this controller
export class N8nWebhookController {
  private readonly logger = new Logger(N8nWebhookController.name);

  constructor(
    // N8nIntegrationService is primarily for outgoing, but might have utils needed here
    // private n8nIntegrationService: N8nIntegrationService,
    private chatService: ChatService,
    private configService: ConfigService,
  ) {}

  /**
   * Endpoint for N8N to send a message from the bot to a specific chat room.
   */
  @Post('bot-message')
  async handleBotMessageFromN8n(
    @Body() messageDto: N8nBotMessageDto,
  ): Promise<any> {
    this.logger.log(
      `Received bot message from N8N for chatRoomId: ${messageDto.chatRoomId}. Content: "${messageDto.content.substring(0, 50)}..."`,
    );
    try {
      const chatRoom = await this.chatService.getChatRoom(
        messageDto.chatRoomId,
      );
      if (!chatRoom) {
        this.logger.warn(
          `Chat room ${messageDto.chatRoomId} not found for N8N bot message.`,
        );
        throw new HttpException('Chat room not found', HttpStatus.NOT_FOUND);
      }

      // It's crucial that chatService.sendBotMessage correctly identifies the sender as 'bot'
      // and handles encryption if necessary.
      const sentMessage = await this.chatService.sendBotMessage({
        chat_room_id: messageDto.chatRoomId,
        content: messageDto.content,
        message_type: messageDto.messageType || 'text',
        metadata: messageDto.metadata,
      });
      this.logger.log(
        `Bot message ${sentMessage.id} successfully processed and sent to room ${messageDto.chatRoomId}.`,
      );

      // Process any subsequent actions requested by N8N in the same payload
      if (messageDto.actions && messageDto.actions.length > 0) {
        this.logger.log(
          `Processing ${messageDto.actions.length} actions from N8N for chat ${messageDto.chatRoomId}.`,
        );
        await this.processN8nActions(
          messageDto.chatRoomId,
          'system_n8n_bot_message',
          messageDto.actions,
        );
      }

      return {
        success: true,
        messageId: sentMessage.id,
        timestamp: sentMessage.created_at,
      };
    } catch (error) {
      this.logger.error(
        `Error handling bot message from N8N for chat ${messageDto.chatRoomId}: ${error.message}`,
        error.stack,
      );
      // Avoid re-throwing generic HttpException if error is already one
      if (error instanceof HttpException) throw error;
      throw new HttpException(
        'Failed to process bot message',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Endpoint for N8N to trigger specific actions on a chat room (e.g., close chat, transfer to operator).
   */
  @Post('chat-action')
  async handleChatActionFromN8n(
    @Body() actionDto: N8nChatActionDto,
  ): Promise<any> {
    this.logger.log(
      `Received chat action from N8N: ${actionDto.action} for chatRoomId: ${actionDto.chatRoomId}. Reason: ${actionDto.reason || 'N/A'}`,
    );
    try {
      await this.processN8nActions(actionDto.chatRoomId, 'system_n8n_action', [
        actionDto,
      ]); // Wrap single action in array
      return {
        success: true,
        action: actionDto.action,
        chatRoomId: actionDto.chatRoomId,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `Error handling chat action from N8N for chat ${actionDto.chatRoomId}, action ${actionDto.action}: ${error.message}`,
        error.stack,
      );
      if (error instanceof HttpException) throw error;
      throw new HttpException(
        'Failed to process chat action',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Centralized processing of actions requested by N8N.
   */
  private async processN8nActions(
    chatRoomId: string,
    initiatorId: string, // e.g., 'system_n8n_bot_message', 'system_n8n_action'
    actions: Array<{
      type: string;
      data?: Record<string, any>;
      reason?: string;
    }>,
  ): Promise<void> {
    for (const action of actions) {
      this.logger.log(
        `Processing N8N action: ${action.type} for chatRoomId: ${chatRoomId}`,
      );
      switch (action.type) {
        case 'transfer_to_operator':
          // The 'reason' for transfer can come from action.data.reason or action.reason
          const transferReason =
            action.data?.reason ||
            action.reason ||
            'Transfer requested by N8N bot';
          const transferResult =
            await this.chatService.requestOperatorEscalation(
              chatRoomId,
              initiatorId,
              transferReason,
            );
          if (!transferResult.success) {
            this.logger.warn(
              `N8N requested operator transfer for chat ${chatRoomId}, but it failed: ${transferResult.message}`,
            );
            // Optionally, notify N8N back if the transfer failed (e.g., via another webhook call if critical)
          }
          break;
        case 'close_chat':
          const closeReason =
            action.data?.reason || action.reason || 'Chat closed by N8N bot';
          await this.chatService.closeChatRoomByBot(chatRoomId, closeReason); // Using specific method for bot closure
          break;
        case 'mark_resolved':
          const resolvedReason =
            action.data?.reason ||
            action.reason ||
            'Chat marked as resolved by N8N bot';
          await this.chatService.markChatResolved(chatRoomId, resolvedReason);
          break;
        // Add cases for 'request_feedback' or other custom actions if needed
        default:
          this.logger.warn(
            `Received unknown N8N action type: ${action.type} for chat ${chatRoomId}.`,
          );
          // Optionally, throw an error or log more details
          // throw new HttpException(`Unsupported N8N action type: ${action.type}`, HttpStatus.BAD_REQUEST);
          break;
      }
    }
  }

  /**
   * Health check endpoint for N8N to verify connectivity.
   * This endpoint should also be protected by N8nAuthGuard if it's not meant for public probing.
   * If N8N uses a simple GET for health checks without signature, this might need a separate, unguarded route or guard modification.
   * For POST with signature, it's fine.
   */
  @Post('health-check') // Changed to POST to align with other signed endpoints
  async n8nHealthCheck(): Promise<any> {
    this.logger.log('N8N health check endpoint called.');
    return {
      status: 'NestJS N8N Integration Endpoint is Healthy',
      timestamp: new Date().toISOString(),
    };
  }
}
```

#### 3.3.2 N8N Authentication Guard

**File Location**: `backend/src/modules/n8n-integration/guards/n8n-auth.guard.ts`
**Module**: `N8nIntegrationModule`.
**Purpose**: Secures incoming webhook calls from N8N by validating a signature.

```typescript
// backend/src/modules/n8n-integration/guards/n8n-auth.guard.ts
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  HttpException,
  HttpStatus,
  RawBodyRequest,
} from '@nestjs/common';
import { N8nIntegrationService } from '../n8n-integration.service'; // For validateIncomingWebhookSignature
import { ConfigService } from '@nestjs/config';
import { Observable } from 'rxjs'; // Required for CanActivate interface

@Injectable()
export class N8nAuthGuard implements CanActivate {
  private readonly logger = new Logger(N8nAuthGuard.name);
  private readonly n8nIncomingWebhookSecret: string | undefined;
  private readonly isN8nIntegrationEnabled: boolean;

  constructor(
    private n8nIntegrationService: N8nIntegrationService, // Used for its validation method
    private configService: ConfigService,
  ) {
    this.n8nIncomingWebhookSecret = this.configService.get<string>(
      'N8N_INCOMING_WEBHOOK_SECRET',
    );
    this.isN8nIntegrationEnabled = this.configService.get<boolean>(
      'ENABLE_N8N_INTEGRATION',
      false,
    );

    if (this.isN8nIntegrationEnabled && !this.n8nIncomingWebhookSecret) {
      this.logger.warn(
        'N8N_INCOMING_WEBHOOK_SECRET is not set while N8N integration is enabled. ' +
          'Incoming webhooks from N8N cannot be securely validated. THIS IS A SECURITY RISK.',
      );
    }
  }

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    if (!this.isN8nIntegrationEnabled) {
      this.logger.warn(
        'N8NAuthGuard: N8N integration is disabled. Denying access to N8N webhook endpoint.',
      );
      // Or, allow but with a clear log that it's only because N8N is off.
      // For security, better to deny if the guard is applied and N8N is off.
      throw new HttpException(
        'N8N Integration is disabled.',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }

    if (!this.n8nIncomingWebhookSecret) {
      this.logger.warn(
        'N8NAuthGuard: N8N_INCOMING_WEBHOOK_SECRET is not configured. ' +
          'Allowing request without signature validation (INSECURE). Endpoint should be protected otherwise.',
      );
      return true; // Allow if secret not set, but this is a security hole.
      // Consider `false` for production if secret is mandatory.
    }

    const request = context.switchToHttp().getRequest<RawBodyRequest<any>>(); // Ensure RawBodyRequest is used
    const signatureFromHeader =
      request.headers['x-n8n-signature'] ||
      request.headers['x-webhook-signature']; // Use a consistent header name

    if (!signatureFromHeader) {
      this.logger.warn(
        'N8NAuthGuard: Missing webhook signature header (e.g., x-n8n-signature) from incoming N8N request.',
      );
      throw new HttpException('Missing signature', HttpStatus.UNAUTHORIZED);
    }

    // Accessing rawBody. This requires specific setup in main.ts:
    // app.use(bodyParser.json({ verify: (req: any, res, buf) => { req.rawBody = buf; } }));
    // OR for NestJS v8+ with Fastify:
    // The `rawBody` might be available via `request.raw`. Check NestJS docs for current best practice.
    // For Express (default):
    // In main.ts:
    // const app = await NestFactory.create(AppModule, { bodyParser: false }); // Disable global body parser
    // app.use(express.raw({ type: 'application/json', verify: (req: any, res, buf) => { req.rawBody = buf; } }));
    // app.use(express.json({ verify: (req: any, res, buf) => { req.rawBody = buf; } })); // Then re-apply json parser also with verify
    // Or more simply if NestJS `json` parser supports `rawBody` option directly in `NestFactory.create`.
    // As of NestJS 9+, you can pass `rawBody: true` to the `json` middleware options.
    // e.g. `app.use(express.json({ rawBody: true }));`
    // The AI implementing this needs to ensure `request.rawBody` is populated.

    const rawBody = request.rawBody;
    if (!rawBody) {
      this.logger.error(
        'N8NAuthGuard: Raw request body not available for N8N signature validation. ' +
          'Ensure `rawBody: true` is set for the JSON body parser in `main.ts` or that `bodyParser` is configured to expose it.',
      );
      throw new HttpException(
        'Internal server error during N8N authentication (raw body missing)',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const payloadString = rawBody.toString('utf8'); // Ensure correct encoding
    const isValid = this.n8nIntegrationService.validateIncomingWebhookSignature(
      payloadString,
      signatureFromHeader as string,
    );

    if (!isValid) {
      this.logger.warn('N8NAuthGuard: Invalid N8N webhook signature.');
      throw new HttpException('Invalid signature', HttpStatus.FORBIDDEN); // Use 403 for invalid signature
    }

    this.logger.debug(
      'N8NAuthGuard: Valid N8N webhook signature. Request allowed.',
    );
    return true;
  }
}
```

**Note on Raw Body for `N8nAuthGuard`**:
The implementing AI must ensure that the raw request body is available to the `N8nAuthGuard`. This typically involves configuring the body parser in `main.ts`.
Example for NestJS with Express (add to `main.ts`):

```typescript
// import * as express from 'express'; // if not already imported
// async function bootstrap() {
//   const app = await NestFactory.create(AppModule, {
//     // If using NestJS built-in JSON parser, it might have an option for rawBody
//   });
//   // For Express, ensure rawBody is captured.
//   // This needs to be done BEFORE NestJS's own global pipes or interceptors might consume the body.
//   // One common way:
//   app.use(express.json({
//     verify: (req: any, res, buf) => {
//       req.rawBody = buf;
//     }
//   }));
//   // Or if using `bodyParser: false` in NestFactory.create and then adding middleware:
//   // app.use(bodyParser.json({ verify: (req: any, res, buf) => { req.rawBody = buf } }));

//   // A simpler way for NestJS v9+ might be:
//   // app.useBodyParser('json', { rawBody: true }); // Check exact API
//   // Or in main.ts:
//   // app.use(json({ verify: (req: any, res, buf) => { req.rawBody = buf.toString() }}));
//   // The key is `req.rawBody = buf;`
//   await app.listen(3000);
// }
// bootstrap();
```

The AI should use the appropriate method for the NestJS version and underlying HTTP server (Express/Fastify).

### 3.4 Phase 4: Module Configuration

This section details how the new services and controllers are organized into NestJS modules and how these modules are integrated.

#### 3.4.1 CommonAppModule

**File Location**: `backend/src/common/common-app.module.ts` (Create if not exists)
**Purpose**: Houses common services like `OfficeHoursService`.

```typescript
// backend/src/common/common-app.module.ts
import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config'; // OfficeHoursService uses ConfigService
import { OfficeHoursService } from './services/office-hours.service';
// Import HttpModule if OfficeHoursService makes HTTP calls for holidays
// import { HttpModule } from '@nestjs/axios';

@Global() // Make services available globally without importing CommonAppModule everywhere
@Module({
  imports: [
    ConfigModule,
    // HttpModule.register({ timeout: 5000, maxRedirects: 5 }), // If holiday API is used
  ],
  providers: [OfficeHoursService],
  exports: [OfficeHoursService],
})
export class CommonAppModule {}
```

#### 3.4.2 N8nIntegrationModule

**File Location**: `backend/src/modules/n8n-integration/n8n-integration.module.ts`
**Purpose**: Encapsulates all N8N-specific components.

```typescript
// backend/src/modules/n8n-integration/n8n-integration.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios'; // For N8nIntegrationService outgoing calls
import { N8nIntegrationService } from './n8n-integration.service';
import { N8nWebhookController } from './controllers/n8n-webhook.controller';
import { N8nAuthGuard } from './guards/n8n-auth.guard';
import { ChatModule } from '../chat/chat.module'; // ForwardRef if circular dependency with ChatService

@Module({
  imports: [
    ConfigModule,
    HttpModule.register({
      // Configure HttpModule for N8nIntegrationService
      timeout: 10000, // Default timeout for N8N calls
      maxRedirects: 3,
    }),
    // ForwardRef if ChatService injects N8nIntegrationService and vice-versa,
    // or if N8nWebhookController needs ChatService which is in ChatModule.
    // For now, assume N8nWebhookController needs ChatService, so ChatModule might be needed.
    // Consider a more decoupled approach if ChatService is not directly needed by controller.
    // If ChatService is needed, ensure ChatModule exports ChatService.
    // For simplicity, if ChatService is only used by N8nWebhookController, it's okay.
    // However, if N8nIntegrationService needs ChatService, that's a circular dep.
    // Let's assume ChatService is provided by ChatModule and N8nWebhookController uses it.
    ChatModule, // This implies ChatModule exports ChatService
  ],
  controllers: [N8nWebhookController],
  providers: [N8nIntegrationService, N8nAuthGuard], // N8nAuthGuard needs N8nIntegrationService
  exports: [N8nIntegrationService], // Export if other modules need to call N8N directly
})
export class N8nIntegrationModule {}
```

**Note on `ChatModule` import**: If `N8nWebhookController` uses `ChatService`, `ChatModule` must be imported here, and `ChatModule` must export `ChatService`. This is a common pattern.

#### 3.4.3 ChatModule Updates

**File Location**: `backend/src/modules/chat/chat.module.ts`
**Purpose**: Integrate `OfficeHoursService` and `N8nIntegrationService` into the chat logic.

```typescript
// backend/src/modules/chat/chat.module.ts
import { Module, forwardRef } from '@nestjs/common';
// ... other existing imports for TypeOrmModule, entities, controllers, services ...
import { OfficeHoursService } from '../../common/services/office-hours.service'; // If CommonAppModule is not Global
import { N8nIntegrationModule } from '../n8n-integration/n8n-integration.module'; // To get N8nIntegrationService
import { N8nIntegrationService } from '../n8n-integration/n8n-integration.service';
// ChatService, ChatGateway, EncryptionService, ChatAuthController, ChatOperatorController etc.

@Module({
  imports: [
    // TypeOrmModule.forFeature([...]),
    // ConfigModule, // If ChatService uses ConfigService directly
    // AuthModule, // For JWT strategy if used by Chat guards
    N8nIntegrationModule, // Provides N8nIntegrationService
    // CommonAppModule, // Not needed if CommonAppModule is Global
  ],
  controllers: [
    /* ChatController, ChatOperatorController */
  ],
  providers: [
    ChatService,
    // ChatAuthService,
    // EncryptionService,
    // ChatGateway,
    // OfficeHoursService, // Provided globally by CommonAppModule if @Global
    // N8nIntegrationService, // Provided by N8nIntegrationModule
    // Ensure all existing providers are listed
  ],
  exports: [
    ChatService /*, other services if needed by other modules like N8nIntegrationModule */,
  ],
})
export class ChatModule {}
```

**Important**:

- If `CommonAppModule` is `@Global()`, `OfficeHoursService` is available everywhere.
- `N8nIntegrationService` is imported from `N8nIntegrationModule`. `ChatService` will inject `N8nIntegrationService`.
- `ChatModule` needs to export `ChatService` if `N8nIntegrationModule` (specifically `N8nWebhookController`) imports `ChatModule` to use `ChatService`.

#### 3.4.4 AppModule Updates

**File Location**: `backend/src/app.module.ts`
**Purpose**: Import new top-level modules.

```typescript
// backend/src/app.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
// ... other core module imports (TypeOrmModule, etc.)
import { CommonAppModule } from './common/common-app.module'; // If not already there
import { N8nIntegrationModule } from './modules/n8n-integration/n8n-integration.module';
import { ChatModule } from './modules/chat/chat.module'; // Assuming it's a feature module
// ... other feature modules

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true, envFilePath: '.env' }),
    // TypeOrmModule.forRootAsync({ ... }),
    CommonAppModule, // For OfficeHoursService etc.
    ChatModule, // Existing chat module
    N8nIntegrationModule, // New N8N module
    // ... other modules
  ],
  // ... controllers, providers for AppModule
})
export class AppModule {}
```

#### Deployment Notes

- Ensure all new environment variables are set in production/staging environments.
- Configure N8N workflows to call the correct NestJS `/api/v1/n8n-hooks/...` endpoints and include the `X-N8N-Signature` header using the `N8N_INCOMING_WEBHOOK_SECRET`.
- Monitor logs closely after deployment for any issues in routing or N8N communication.
