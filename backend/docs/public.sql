/*
 Navicat Premium Data Transfer

 Source Server         : db-dev-postgres
 Source Server Type    : PostgreSQL
 Source Server Version : 140017 (140017)
 Source Host           : **************:5432
 Source Catalog        : dbcsrit
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 140017 (140017)
 File Encoding         : 65001

 Date: 23/06/2025 09:37:41
*/


-- ----------------------------
-- Table structure for artikel
-- ----------------------------
DROP TABLE IF EXISTS "public"."artikel";
CREATE TABLE "public"."artikel" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "judul" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "slug" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "kategori" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "foto" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "isi" text COLLATE "pg_catalog"."default" NOT NULL,
  "tgl_posting" timestamp(6) NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "file_pdf" varchar COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."artikel" OWNER TO "aistech";

-- ----------------------------
-- Table structure for berita
-- ----------------------------
DROP TABLE IF EXISTS "public"."berita";
CREATE TABLE "public"."berita" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "judul" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "slug" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "kategori" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "foto" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "isi" text COLLATE "pg_catalog"."default" NOT NULL,
  "tgl_posting" timestamp(6) NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
ALTER TABLE "public"."berita" OWNER TO "aistech";

-- ----------------------------
-- Table structure for bulan
-- ----------------------------
DROP TABLE IF EXISTS "public"."bulan";
CREATE TABLE "public"."bulan" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "nama_bulan" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
ALTER TABLE "public"."bulan" OWNER TO "aistech";

-- ----------------------------
-- Table structure for chat_messages
-- ----------------------------
DROP TABLE IF EXISTS "public"."chat_messages";
CREATE TABLE "public"."chat_messages" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "sender_type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "content" text COLLATE "pg_catalog"."default" NOT NULL,
  "message_type" varchar COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'text'::character varying,
  "attachment_url" varchar COLLATE "pg_catalog"."default",
  "attachment_name" varchar COLLATE "pg_catalog"."default",
  "attachment_size" int4,
  "status" varchar COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'sent'::character varying,
  "is_edited" bool NOT NULL DEFAULT false,
  "edited_at" timestamp(6),
  "reply_to_message_id" varchar COLLATE "pg_catalog"."default",
  "is_deleted" bool NOT NULL DEFAULT false,
  "deleted_at" timestamp(6),
  "metadata" varchar COLLATE "pg_catalog"."default",
  "timestamp" timestamp(6) NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "chatRoomId" uuid,
  "senderGuestUserId" uuid,
  "senderSystemUserId" uuid
)
;
ALTER TABLE "public"."chat_messages" OWNER TO "aistech";
COMMENT ON COLUMN "public"."chat_messages"."sender_type" IS 'guest_user, system_user, system';
COMMENT ON COLUMN "public"."chat_messages"."message_type" IS 'text, image, file, system';
COMMENT ON COLUMN "public"."chat_messages"."status" IS 'sent, delivered, read, failed';

-- ----------------------------
-- Table structure for chat_operators
-- ----------------------------
DROP TABLE IF EXISTS "public"."chat_operators";
CREATE TABLE "public"."chat_operators" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "username" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "password_hash" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "role" varchar COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'operator'::character varying,
  "name" varchar COLLATE "pg_catalog"."default",
  "email" varchar COLLATE "pg_catalog"."default",
  "is_active" bool NOT NULL DEFAULT true,
  "last_seen" timestamp(6),
  "is_online" bool NOT NULL DEFAULT false,
  "currentHashedRefreshToken" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
ALTER TABLE "public"."chat_operators" OWNER TO "aistech";
COMMENT ON COLUMN "public"."chat_operators"."role" IS 'operator, admin, supervisor';

-- ----------------------------
-- Table structure for chat_room_participants
-- ----------------------------
DROP TABLE IF EXISTS "public"."chat_room_participants";
CREATE TABLE "public"."chat_room_participants" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "participant_type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "status" varchar COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'active'::character varying,
  "joined_at" timestamp(6),
  "left_at" timestamp(6),
  "last_read_message_id" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "chatRoomId" uuid,
  "guestUserId" uuid,
  "systemUserId" uuid
)
;
ALTER TABLE "public"."chat_room_participants" OWNER TO "aistech";
COMMENT ON COLUMN "public"."chat_room_participants"."participant_type" IS 'guest_user, system_user';
COMMENT ON COLUMN "public"."chat_room_participants"."status" IS 'active, left, kicked';

-- ----------------------------
-- Table structure for chat_rooms
-- ----------------------------
DROP TABLE IF EXISTS "public"."chat_rooms";
CREATE TABLE "public"."chat_rooms" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "type" varchar COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'user-operator'::character varying,
  "title" varchar COLLATE "pg_catalog"."default",
  "status" varchar COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'active'::character varying,
  "last_message_at" timestamp(6),
  "closed_at" timestamp(6),
  "closed_by_type" varchar COLLATE "pg_catalog"."default",
  "closed_by_id" varchar COLLATE "pg_catalog"."default",
  "current_handler" varchar COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'unassigned'::character varying,
  "handler_assigned_at" timestamp(6),
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
ALTER TABLE "public"."chat_rooms" OWNER TO "aistech";
COMMENT ON COLUMN "public"."chat_rooms"."type" IS 'user-operator, group, system';
COMMENT ON COLUMN "public"."chat_rooms"."status" IS 'active, closed, archived';
COMMENT ON COLUMN "public"."chat_rooms"."current_handler" IS 'operator, bot, unassigned, pending_assignment';

-- ----------------------------
-- Table structure for chat_users
-- ----------------------------
DROP TABLE IF EXISTS "public"."chat_users";
CREATE TABLE "public"."chat_users" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "email" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "phone" varchar COLLATE "pg_catalog"."default",
  "password_hash" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "verification_status" varchar COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'pending'::character varying,
  "verification_token" varchar COLLATE "pg_catalog"."default",
  "verification_expires_at" timestamp(6),
  "name" varchar COLLATE "pg_catalog"."default",
  "is_active" bool NOT NULL DEFAULT true,
  "last_seen" timestamp(6),
  "currentHashedRefreshToken" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
ALTER TABLE "public"."chat_users" OWNER TO "aistech";
COMMENT ON COLUMN "public"."chat_users"."verification_status" IS 'pending, verified, suspended';

-- ----------------------------
-- Table structure for event
-- ----------------------------
DROP TABLE IF EXISTS "public"."event";
CREATE TABLE "public"."event" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "judul" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "tgl_awal" varchar COLLATE "pg_catalog"."default",
  "tgl_akhir" varchar COLLATE "pg_catalog"."default",
  "files" text[] COLLATE "pg_catalog"."default",
  "tempat" varchar COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."event" OWNER TO "aistech";

-- ----------------------------
-- Table structure for hak_akses
-- ----------------------------
DROP TABLE IF EXISTS "public"."hak_akses";
CREATE TABLE "public"."hak_akses" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "id_menu" uuid,
  "roleId" uuid
)
;
ALTER TABLE "public"."hak_akses" OWNER TO "aistech";

-- ----------------------------
-- Table structure for informasi_umum
-- ----------------------------
DROP TABLE IF EXISTS "public"."informasi_umum";
CREATE TABLE "public"."informasi_umum" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "logo" varchar COLLATE "pg_catalog"."default",
  "tentang" varchar COLLATE "pg_catalog"."default",
  "deskripsi" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "foto_visi" varchar COLLATE "pg_catalog"."default",
  "visi" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "foto_misi" varchar COLLATE "pg_catalog"."default",
  "misi" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "gambar_header" varchar COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."informasi_umum" OWNER TO "aistech";

-- ----------------------------
-- Table structure for kontak
-- ----------------------------
DROP TABLE IF EXISTS "public"."kontak";
CREATE TABLE "public"."kontak" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "alamat" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "no_telp" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "email" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "lat" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "long" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "facebook" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "instagram" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "twitter" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "website" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
ALTER TABLE "public"."kontak" OWNER TO "aistech";

-- ----------------------------
-- Table structure for layanan
-- ----------------------------
DROP TABLE IF EXISTS "public"."layanan";
CREATE TABLE "public"."layanan" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "judul" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "slug" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "kategori" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "foto" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "isi" text COLLATE "pg_catalog"."default" NOT NULL,
  "tgl_posting" timestamp(6) NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
ALTER TABLE "public"."layanan" OWNER TO "aistech";

-- ----------------------------
-- Table structure for master_progres_ticket
-- ----------------------------
DROP TABLE IF EXISTS "public"."master_progres_ticket";
CREATE TABLE "public"."master_progres_ticket" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "color" varchar COLLATE "pg_catalog"."default" NOT NULL
)
;
ALTER TABLE "public"."master_progres_ticket" OWNER TO "aistech";

-- ----------------------------
-- Table structure for master_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."master_role";
CREATE TABLE "public"."master_role" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "ket" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
ALTER TABLE "public"."master_role" OWNER TO "aistech";

-- ----------------------------
-- Table structure for menu
-- ----------------------------
DROP TABLE IF EXISTS "public"."menu";
CREATE TABLE "public"."menu" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "jenis_menu" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "nama_menu" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "id_menu" uuid,
  "posisi" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "link" varchar COLLATE "pg_catalog"."default",
  "icon" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
ALTER TABLE "public"."menu" OWNER TO "aistech";

-- ----------------------------
-- Table structure for monitoring
-- ----------------------------
DROP TABLE IF EXISTS "public"."monitoring";
CREATE TABLE "public"."monitoring" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "file" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "foto" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "nama" text COLLATE "pg_catalog"."default",
  "jenis" text COLLATE "pg_catalog"."default",
  "tahunId" uuid,
  "bulan" text COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."monitoring" OWNER TO "aistech";

-- ----------------------------
-- Table structure for panduan_keamanan
-- ----------------------------
DROP TABLE IF EXISTS "public"."panduan_keamanan";
CREATE TABLE "public"."panduan_keamanan" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "file" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "foto" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "desc" text COLLATE "pg_catalog"."default",
  "nama" varchar COLLATE "pg_catalog"."default" NOT NULL
)
;
ALTER TABLE "public"."panduan_keamanan" OWNER TO "aistech";

-- ----------------------------
-- Table structure for panduan_pedoman
-- ----------------------------
DROP TABLE IF EXISTS "public"."panduan_pedoman";
CREATE TABLE "public"."panduan_pedoman" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "judul" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "file_pdf" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "tanggal" timestamp(6) NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
ALTER TABLE "public"."panduan_pedoman" OWNER TO "aistech";

-- ----------------------------
-- Table structure for peringatan_keamanan
-- ----------------------------
DROP TABLE IF EXISTS "public"."peringatan_keamanan";
CREATE TABLE "public"."peringatan_keamanan" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "file_pdf" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "foto" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "isi" text COLLATE "pg_catalog"."default",
  "tgl_posting" timestamp(6) NOT NULL,
  "judul" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "link_sumber" text COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."peringatan_keamanan" OWNER TO "aistech";

-- ----------------------------
-- Table structure for rfc_file
-- ----------------------------
DROP TABLE IF EXISTS "public"."rfc_file";
CREATE TABLE "public"."rfc_file" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "file_pdf" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "judul" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "kategori" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "tgl_posting" timestamp(6) NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "isi" text COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."rfc_file" OWNER TO "aistech";

-- ----------------------------
-- Table structure for riwayat_ticket
-- ----------------------------
DROP TABLE IF EXISTS "public"."riwayat_ticket";
CREATE TABLE "public"."riwayat_ticket" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "ket" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "ticketId" uuid NOT NULL,
  "progresId" uuid
)
;
ALTER TABLE "public"."riwayat_ticket" OWNER TO "aistech";

-- ----------------------------
-- Table structure for tahun
-- ----------------------------
DROP TABLE IF EXISTS "public"."tahun";
CREATE TABLE "public"."tahun" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "nama_tahun" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
ALTER TABLE "public"."tahun" OWNER TO "aistech";

-- ----------------------------
-- Table structure for ticketing
-- ----------------------------
DROP TABLE IF EXISTS "public"."ticketing";
CREATE TABLE "public"."ticketing" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "number" varchar COLLATE "pg_catalog"."default",
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "attachment" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "email" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "subject" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "body" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "progresId" uuid,
  "ket" varchar COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."ticketing" OWNER TO "aistech";

-- ----------------------------
-- Table structure for user_activity
-- ----------------------------
DROP TABLE IF EXISTS "public"."user_activity";
CREATE TABLE "public"."user_activity" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "ip" varchar COLLATE "pg_catalog"."default",
  "device" varchar COLLATE "pg_catalog"."default",
  "username" varchar COLLATE "pg_catalog"."default",
  "route" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "action" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
ALTER TABLE "public"."user_activity" OWNER TO "aistech";

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS "public"."users";
CREATE TABLE "public"."users" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "nama" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "username" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "password" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "email" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "foto" varchar COLLATE "pg_catalog"."default",
  "currentHashedRefreshToken" varchar COLLATE "pg_catalog"."default",
  "forgetPasswordToken" varchar COLLATE "pg_catalog"."default",
  "status" int4 NOT NULL DEFAULT 1,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "roleId" uuid
)
;
ALTER TABLE "public"."users" OWNER TO "aistech";
COMMENT ON COLUMN "public"."users"."status" IS '1 aktif, 0 tidak aktif';

-- ----------------------------
-- Function structure for uuid_generate_v1
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v1"();
CREATE OR REPLACE FUNCTION "public"."uuid_generate_v1"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v1'
  LANGUAGE c VOLATILE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_generate_v1"() OWNER TO "postgres";

-- ----------------------------
-- Function structure for uuid_generate_v1mc
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v1mc"();
CREATE OR REPLACE FUNCTION "public"."uuid_generate_v1mc"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v1mc'
  LANGUAGE c VOLATILE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_generate_v1mc"() OWNER TO "postgres";

-- ----------------------------
-- Function structure for uuid_generate_v3
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v3"("namespace" uuid, "name" text);
CREATE OR REPLACE FUNCTION "public"."uuid_generate_v3"("namespace" uuid, "name" text)
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v3'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_generate_v3"("namespace" uuid, "name" text) OWNER TO "postgres";

-- ----------------------------
-- Function structure for uuid_generate_v4
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v4"();
CREATE OR REPLACE FUNCTION "public"."uuid_generate_v4"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v4'
  LANGUAGE c VOLATILE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_generate_v4"() OWNER TO "postgres";

-- ----------------------------
-- Function structure for uuid_generate_v5
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v5"("namespace" uuid, "name" text);
CREATE OR REPLACE FUNCTION "public"."uuid_generate_v5"("namespace" uuid, "name" text)
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v5'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_generate_v5"("namespace" uuid, "name" text) OWNER TO "postgres";

-- ----------------------------
-- Function structure for uuid_nil
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_nil"();
CREATE OR REPLACE FUNCTION "public"."uuid_nil"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_nil'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_nil"() OWNER TO "postgres";

-- ----------------------------
-- Function structure for uuid_ns_dns
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_ns_dns"();
CREATE OR REPLACE FUNCTION "public"."uuid_ns_dns"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_ns_dns'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_ns_dns"() OWNER TO "postgres";

-- ----------------------------
-- Function structure for uuid_ns_oid
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_ns_oid"();
CREATE OR REPLACE FUNCTION "public"."uuid_ns_oid"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_ns_oid'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_ns_oid"() OWNER TO "postgres";

-- ----------------------------
-- Function structure for uuid_ns_url
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_ns_url"();
CREATE OR REPLACE FUNCTION "public"."uuid_ns_url"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_ns_url'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_ns_url"() OWNER TO "postgres";

-- ----------------------------
-- Function structure for uuid_ns_x500
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_ns_x500"();
CREATE OR REPLACE FUNCTION "public"."uuid_ns_x500"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_ns_x500'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_ns_x500"() OWNER TO "postgres";

-- ----------------------------
-- Primary Key structure for table artikel
-- ----------------------------
ALTER TABLE "public"."artikel" ADD CONSTRAINT "PK_bba1367c30e25eaba3e75f92b36" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table berita
-- ----------------------------
ALTER TABLE "public"."berita" ADD CONSTRAINT "PK_b5624a3586e969e46da38ec2e5e" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bulan
-- ----------------------------
ALTER TABLE "public"."bulan" ADD CONSTRAINT "PK_a40e5c5cb129468399c546ca989" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table chat_messages
-- ----------------------------
ALTER TABLE "public"."chat_messages" ADD CONSTRAINT "PK_40c55ee0e571e268b0d3cd37d10" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table chat_operators
-- ----------------------------
ALTER TABLE "public"."chat_operators" ADD CONSTRAINT "UQ_14863048762eeb96a164d606543" UNIQUE ("username");

-- ----------------------------
-- Primary Key structure for table chat_operators
-- ----------------------------
ALTER TABLE "public"."chat_operators" ADD CONSTRAINT "PK_e1c9c1c706afb8a751cd32df692" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table chat_room_participants
-- ----------------------------
ALTER TABLE "public"."chat_room_participants" ADD CONSTRAINT "PK_2fb4058c329ab4f75ba14443764" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table chat_rooms
-- ----------------------------
ALTER TABLE "public"."chat_rooms" ADD CONSTRAINT "PK_c69082bd83bffeb71b0f455bd59" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table chat_users
-- ----------------------------
ALTER TABLE "public"."chat_users" ADD CONSTRAINT "UQ_8527a255c911f9e762cbf574c3d" UNIQUE ("email");
ALTER TABLE "public"."chat_users" ADD CONSTRAINT "UQ_4f65125709b169558edb353c1e3" UNIQUE ("phone");

-- ----------------------------
-- Primary Key structure for table chat_users
-- ----------------------------
ALTER TABLE "public"."chat_users" ADD CONSTRAINT "PK_45f77b7b90225045d6bf7f13756" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table event
-- ----------------------------
ALTER TABLE "public"."event" ADD CONSTRAINT "PK_30c2f3bbaf6d34a55f8ae6e4614" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table hak_akses
-- ----------------------------
ALTER TABLE "public"."hak_akses" ADD CONSTRAINT "PK_76aa26e15082a45b8b7940e2612" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table informasi_umum
-- ----------------------------
ALTER TABLE "public"."informasi_umum" ADD CONSTRAINT "PK_d6872204891c0932f1bbc7e51f0" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table kontak
-- ----------------------------
ALTER TABLE "public"."kontak" ADD CONSTRAINT "PK_ea9a66600c5413a58273672531f" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table layanan
-- ----------------------------
ALTER TABLE "public"."layanan" ADD CONSTRAINT "PK_e7fe1b002365bc463d02e35915e" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table master_progres_ticket
-- ----------------------------
ALTER TABLE "public"."master_progres_ticket" ADD CONSTRAINT "PK_c60c57f64a1dc48e6abe0425c6c" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table master_role
-- ----------------------------
ALTER TABLE "public"."master_role" ADD CONSTRAINT "PK_e074bf34e6b6052ee781b384315" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table menu
-- ----------------------------
ALTER TABLE "public"."menu" ADD CONSTRAINT "PK_35b2a8f47d153ff7a41860cceeb" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table monitoring
-- ----------------------------
ALTER TABLE "public"."monitoring" ADD CONSTRAINT "PK_22a9f9562020245a98bd2c4fb3c" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table panduan_keamanan
-- ----------------------------
ALTER TABLE "public"."panduan_keamanan" ADD CONSTRAINT "PK_d40dbbac8c584d1455d8b7624ff" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table panduan_pedoman
-- ----------------------------
ALTER TABLE "public"."panduan_pedoman" ADD CONSTRAINT "PK_3ee284296fbc49a88c0221515e1" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table peringatan_keamanan
-- ----------------------------
ALTER TABLE "public"."peringatan_keamanan" ADD CONSTRAINT "PK_f40ff2b0674ac848cf8268d7fc1" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table rfc_file
-- ----------------------------
ALTER TABLE "public"."rfc_file" ADD CONSTRAINT "PK_8356b00d4d9967bbc4637e0e5a6" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table riwayat_ticket
-- ----------------------------
ALTER TABLE "public"."riwayat_ticket" ADD CONSTRAINT "PK_c96e296375f264dfe89fcaf370b" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tahun
-- ----------------------------
ALTER TABLE "public"."tahun" ADD CONSTRAINT "PK_e492d770b585ddf9bc331b73c8f" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table ticketing
-- ----------------------------
ALTER TABLE "public"."ticketing" ADD CONSTRAINT "PK_5bc92fa54186fd63dfbcdd42aef" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table user_activity
-- ----------------------------
ALTER TABLE "public"."user_activity" ADD CONSTRAINT "PK_daec6d19443689bda7d7785dff5" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table users
-- ----------------------------
ALTER TABLE "public"."users" ADD CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email");
ALTER TABLE "public"."users" ADD CONSTRAINT "UQ_fe0bb3f6520ee0469504521e710" UNIQUE ("username");

-- ----------------------------
-- Primary Key structure for table users
-- ----------------------------
ALTER TABLE "public"."users" ADD CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table chat_messages
-- ----------------------------
ALTER TABLE "public"."chat_messages" ADD CONSTRAINT "FK_1d31d7a01118daedc228a890635" FOREIGN KEY ("senderSystemUserId") REFERENCES "public"."users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."chat_messages" ADD CONSTRAINT "FK_c7fd35e9a8cb40b91bb014441e2" FOREIGN KEY ("chatRoomId") REFERENCES "public"."chat_rooms" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."chat_messages" ADD CONSTRAINT "FK_f69e1f0bcc4830fd2e58450f0d7" FOREIGN KEY ("senderGuestUserId") REFERENCES "public"."chat_users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table chat_room_participants
-- ----------------------------
ALTER TABLE "public"."chat_room_participants" ADD CONSTRAINT "FK_45f001549563ec90b01f8f01008" FOREIGN KEY ("chatRoomId") REFERENCES "public"."chat_rooms" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."chat_room_participants" ADD CONSTRAINT "FK_f801d11195e6e1bba3f88d74601" FOREIGN KEY ("systemUserId") REFERENCES "public"."users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."chat_room_participants" ADD CONSTRAINT "FK_fe0e7b6ba7d25b7af76e1e71625" FOREIGN KEY ("guestUserId") REFERENCES "public"."chat_users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table hak_akses
-- ----------------------------
ALTER TABLE "public"."hak_akses" ADD CONSTRAINT "FK_bf450db1f0594344d91528a6c80" FOREIGN KEY ("roleId") REFERENCES "public"."master_role" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table monitoring
-- ----------------------------
ALTER TABLE "public"."monitoring" ADD CONSTRAINT "FK_598bf47ed0a7ab5d6361e495f90" FOREIGN KEY ("tahunId") REFERENCES "public"."tahun" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table riwayat_ticket
-- ----------------------------
ALTER TABLE "public"."riwayat_ticket" ADD CONSTRAINT "FK_69c787192d75fe65c44ea200a7a" FOREIGN KEY ("ticketId") REFERENCES "public"."ticketing" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."riwayat_ticket" ADD CONSTRAINT "FK_7250016650485a5dcacbff5c87c" FOREIGN KEY ("progresId") REFERENCES "public"."master_progres_ticket" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table ticketing
-- ----------------------------
ALTER TABLE "public"."ticketing" ADD CONSTRAINT "FK_2c6491a870c025f960edd7e6dde" FOREIGN KEY ("progresId") REFERENCES "public"."master_progres_ticket" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table users
-- ----------------------------
ALTER TABLE "public"."users" ADD CONSTRAINT "FK_368e146b785b574f42ae9e53d5e" FOREIGN KEY ("roleId") REFERENCES "public"."master_role" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
