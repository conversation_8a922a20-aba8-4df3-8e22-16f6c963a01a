# File Access Logging Implementation Guide

## Overview

This guide explains how to implement file access logging for secured file download endpoints and how to properly access uploaded files that have been renamed with secure UUIDs.

## 1. File Access Logging Implementation

### Step 1: Add AuditService to Module

To add file access logging to any module, you need to include the `AuditService` in the module's providers:

```typescript
// Example: src/modules/your-module/your-module.module.ts
import { Module } from '@nestjs/common';
import { AuditService } from 'src/common/services/audit.service';
// ... other imports

@Module({
  imports: [
    /* your imports */
  ],
  controllers: [YourController],
  providers: [
    YourService,
    AuditService, // Add this line
    // ... other providers
  ],
})
export class YourModule {}
```

### Step 2: Inject AuditService in Controller

Add the `AuditService` to your controller's constructor:

```typescript
// Example: src/modules/your-module/your-module.controller.ts
import { AuditService } from 'src/common/services/audit.service';

@Controller('api/v1/your-module')
export class YourController {
  constructor(
    private readonly yourService: YourService,
    private readonly auditService: AuditService, // Add this line
    // ... other services
  ) {}
}
```

### Step 3: Implement Logging in Secured Endpoints

For secured file download endpoints, wrap your logic in try-catch blocks and add logging:

```typescript
@Get('download/:id')
@SecureFileEndpoint()  // This applies JWT authentication
async downloadFile(
  @Param('id') id: string,
  @Res() res: Response,
  @Request() req,
) {
  try {
    // Check if user has access to this file
    const hasAccess = await this.yourService.checkFileAccess(id, req.user.id);
    if (!hasAccess) {
      // Log failed access attempt
      await this.auditService.logFailedAccess(
        req.user.id,
        id,
        'download',
        req.ip,
        'Access denied - insufficient permissions'
      );
      throw new ForbiddenException('Access denied to this file');
    }

    // Get file data and serve file
    const fileData = await this.yourService.findOne(id);
    const filePath = join('./uploads/your-folder/', fileData.filename);

    await this.fileAccessService.serveFile(filePath, res, {
      contentType: 'application/pdf',
      filename: `secure_file_${fileData.filename}`,
      inline: false,
    });

    // Log successful file access
    await this.auditService.logSuccessfulAccess(
      req.user.id,
      id,
      'download',
      req.ip,
      `File downloaded: ${fileData.filename}`
    );
  } catch (error) {
    if (error instanceof ForbiddenException) {
      // Already logged above, just re-throw
      throw error;
    } else {
      // Log other errors
      await this.auditService.logFailedAccess(
        req.user?.id || 'unknown',
        id,
        'download',
        req.ip,
        `Error: ${error.message}`
      );
      throw error;
    }
  }
}
```

### Step 4: Available Logging Methods

The `AuditService` provides several convenient methods:

```typescript
// Log successful access
await this.auditService.logSuccessfulAccess(
  userId: string,
  fileId: string,
  action: string,
  ipAddress: string,
  details?: string
);

// Log failed access
await this.auditService.logFailedAccess(
  userId: string,
  fileId: string,
  action: string,
  ipAddress: string,
  details?: string
);

// Custom logging with full control
await this.auditService.logFileAccess({
  userId: 'user123',
  fileId: 'file456',
  action: 'download',
  timestamp: new Date(),
  ipAddress: '***********',
  success: true,
  details: 'Custom details'
});
```

## 2. Accessing Uploaded Files with UUID Names

### Understanding File Naming

When files are uploaded using the secure upload system, they are renamed with UUIDs for security:

- **Original filename**: `document.pdf`
- **Stored filename**: `a1b2c3d4-e5f6-7890-abcd-ef1234567890.pdf`
- **Database stores**: The UUID filename
- **Original name**: Can be stored separately if needed

### Step 1: File Upload Process

During upload, files are processed as follows:

```typescript
// In your upload endpoint
@Post('upload')
@UseInterceptors(FileInterceptor('file'))
async uploadFile(
  @UploadedFile() file: Express.Multer.File,
  @Body() data: CreateDto,
) {
  let secureFilename;

  if (file) {
    // Generate secure UUID-based filename
    secureFilename = StorageConfig.generateSecureFilename(file.originalname);

    // Store file with UUID name
    const secureFilePath = StorageConfig.getSecureFilePath(secureFilename);
    const ws = createWriteStream(secureFilePath);
    ws.write(file.buffer);
  }

  // Save to database with UUID filename
  const record = await this.service.create({
    ...data,
    filename: secureFilename,        // UUID filename for storage
    originalName: file.originalname, // Optional: store original name
  });

  return { success: true, data: record };
}
```

### Step 2: File Download/Access Process

When accessing files, use the UUID filename stored in the database:

```typescript
@Get('download/:id')
@SecureFileEndpoint()
async downloadFile(
  @Param('id') id: string,
  @Res() res: Response,
  @Request() req,
) {
  // Get record from database
  const fileRecord = await this.service.findOne(id);

  // Use the UUID filename for file system access
  const filePath = join('./uploads/your-folder/', fileRecord.filename);

  // Serve file with user-friendly name
  await this.fileAccessService.serveFile(filePath, res, {
    contentType: 'application/pdf',
    filename: fileRecord.originalName || `download_${fileRecord.filename}`,
    inline: false,
  });
}
```

### Step 3: File Path Construction

Always construct file paths using the stored UUID filename:

```typescript
// ✅ Correct - using UUID filename from database
const filePath = join('./uploads/rfc/', fileRecord.filename);

// ❌ Wrong - using original filename
const filePath = join('./uploads/rfc/', fileRecord.originalName);

// ✅ Correct - using StorageConfig helper
const filePath = StorageConfig.getSecureFilePath(fileRecord.filename);
```

### Step 4: Base64 File Serving

For endpoints that return base64 content:

```typescript
@Get('base64/:id')
async getFileAsBase64(@Param('id') id: string) {
  const fileRecord = await this.service.findOne(id);

  // Read file using UUID filename
  const filePath = join('./uploads/your-folder/', fileRecord.filename);
  const base64Content = await this.service.readFileAsBase64(filePath);

  return {
    success: true,
    data: {
      ...fileRecord,
      base64: base64Content,
      // Optionally include original filename for display
      displayName: fileRecord.originalName,
    },
  };
}
```

## 3. Complete Example Implementation

Here's a complete example showing both logging and file access:

```typescript
// your-module.controller.ts
import { AuditService } from 'src/common/services/audit.service';
import { FileAccessService } from 'src/common/services/file-access.service';
import { SecureFileEndpoint } from 'src/common/decorators/secure-file-endpoint.decorator';

@Controller('api/v1/documents')
export class DocumentController {
  constructor(
    private readonly documentService: DocumentService,
    private readonly auditService: AuditService,
    private readonly fileAccessService: FileAccessService,
  ) {}

  @Get('download/:id')
  @SecureFileEndpoint()
  async downloadDocument(
    @Param('id') id: string,
    @Res() res: Response,
    @Request() req,
  ) {
    try {
      // Check access permissions
      const hasAccess = await this.documentService.checkFileAccess(
        id,
        req.user.id,
      );
      if (!hasAccess) {
        await this.auditService.logFailedAccess(
          req.user.id,
          id,
          'download',
          req.ip,
          'Access denied - insufficient permissions',
        );
        throw new ForbiddenException('Access denied');
      }

      // Get document record (contains UUID filename)
      const document = await this.documentService.findOne(id);

      // Construct file path using UUID filename
      const filePath = join('./uploads/documents/', document.filename);

      // Serve file
      await this.fileAccessService.serveFile(filePath, res, {
        contentType: 'application/pdf',
        filename: document.originalName || `document_${document.id}.pdf`,
        inline: false,
      });

      // Log successful access
      await this.auditService.logSuccessfulAccess(
        req.user.id,
        id,
        'download',
        req.ip,
        `Document downloaded: ${document.originalName} (${document.filename})`,
      );
    } catch (error) {
      if (!(error instanceof ForbiddenException)) {
        await this.auditService.logFailedAccess(
          req.user?.id || 'unknown',
          id,
          'download',
          req.ip,
          `Error: ${error.message}`,
        );
      }
      throw error;
    }
  }
}
```

## 4. Log Output Format

The audit logs are currently output to console in JSON format:

```json
{
  "timestamp": "2025-06-02T09:15:30.123Z",
  "userId": "user123",
  "fileId": "doc456",
  "action": "download",
  "ipAddress": "*************",
  "success": true,
  "details": "RFC file downloaded: a1b2c3d4-e5f6-7890-abcd-ef1234567890.pdf"
}
```

## 5. Security Best Practices

1. **Always use UUID filenames** for file system storage
2. **Store original filenames separately** if needed for display
3. **Validate file access permissions** before serving files
4. **Log both successful and failed access attempts**
5. **Include meaningful details** in log entries
6. **Use the FileAccessService** for secure file serving
7. **Apply @SecureFileEndpoint()** decorator to protected endpoints

## 6. Future Enhancements

The current implementation logs to console. Future enhancements may include:

- Database persistence for audit logs
- Integration with external logging services
- Automated alerts for suspicious access patterns
- Audit report generation
- Log retention policies

## 7. Troubleshooting

### File Not Found Errors

- Ensure you're using the UUID filename from the database
- Check that the file path construction is correct
- Verify the upload directory exists and has proper permissions

### Access Denied Errors

- Confirm the user has proper permissions
- Check that the JWT token is valid
- Verify the file access check logic

### Logging Issues

- Ensure AuditService is properly injected
- Check that the module includes AuditService in providers
- Verify request object contains user and IP information
