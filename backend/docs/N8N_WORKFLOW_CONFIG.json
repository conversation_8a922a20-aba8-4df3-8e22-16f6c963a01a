{"name": "Aistech Chat Bot Integration", "nodes": [{"parameters": {"httpMethod": "POST", "path": "chat-webhook", "options": {}}, "id": "webhook-trigger", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "Aistech-chat"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.eventType}}", "operation": "equal", "value2": "new_chat"}]}}, "id": "check-event-type", "name": "Check Event Type", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.eventType}}", "operation": "equal", "value2": "new_message"}]}}, "id": "check-message-event", "name": "Check Message Event", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 500]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.eventType}}", "operation": "equal", "value2": "user_escalation_request"}]}}, "id": "check-escalation", "name": "Check Escalation Request", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 700]}, {"parameters": {"jsCode": "// Welcome message for new chat\nconst chatRoomId = $input.first().json.chatRoomId;\nconst userId = $input.first().json.userId;\nconst metadata = $input.first().json.metadata;\n\n// Generate welcome message\nconst welcomeMessage = `Selamat datang di Aistech! 👋\\n\\nSaya adalah asisten virtual yang siap membantu Anda dengan:\\n• Informasi produk dan layanan\\n• Pertanyaan umum perbankan\\n• Panduan transaksi\\n• Dan bantuan lainnya\\n\\nSilakan ketik pertanyaan Anda atau ketik \"operator\" jika ingin berbicara dengan petugas kami.`;\n\nreturn {\n  chatRoomId,\n  userId,\n  content: welcomeMessage,\n  messageType: 'text',\n  metadata: {\n    quickReplies: [\n      { title: '💳 Produk & Layanan', payload: 'products' },\n      { title: '📞 Hubungi Operator', payload: 'operator' },\n      { title: '❓ FAQ', payload: 'faq' },\n      { title: '🏦 Lokasi Cabang', payload: 'branches' }\n    ]\n  }\n};"}, "id": "welcome-message", "name": "Generate Welcome Message", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 200]}, {"parameters": {"jsCode": "// Prepare user message for AI processing\nconst message = $input.first().json.message;\nconst chatRoomId = $input.first().json.chatRoomId;\nconst userId = $input.first().json.userId;\nconst content = message.content;\n\n// Check for explicit operator request first\nif (content.toLowerCase().includes('operator') || content.toLowerCase().includes('petugas') || content.toLowerCase().includes('manusia')) {\n  return {\n    chatRoomId,\n    content: 'Baik, saya akan menghubungkan Anda dengan petugas kami. Mohon tunggu sebentar...',\n    messageType: 'text',\n    actions: [{\n      type: 'transfer_to_operator',\n      reason: 'User requested human operator'\n    }]\n  };\n}\n\n// Prepare for AI processing\nreturn {\n  chatRoomId,\n  userId,\n  userMessage: content,\n  messageType: 'text'\n};"}, "id": "prepare-ai-input", "name": "Prepare AI Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 500]}, {"parameters": {"url": "https://openrouter.ai/api/v1/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "httpMethod": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{$credentials.openRouterApiKey}}"}, {"name": "Content-Type", "value": "application/json"}, {"name": "HTTP-Referer", "value": "https://Aistech.id"}, {"name": "X-Title", "value": "Aiste<PERSON> Chat Assistant"}]}, "sendBody": true, "jsonBody": "{\n  \"model\": \"anthropic/claude-3.5-sonnet\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"Anda adalah asisten virtual Aistech yang ramah dan profesional. Anda membantu nasabah dengan informasi perbankan, produk, dan layanan <PERSON>.\\n\\nInformasi Aistech:\\n- Bank Pembangunan Daerah Sulawesi Selatan\\n- Kantor Pusat: Jl. Dr. Ratulangi No. 16, Makassar\\n- Jam operasional: Senin-Minggu 08:00-17:00 WITA\\n\\nProduk & Layanan:\\n💳 Simpanan: Tabungan Reguler, Tabungan Berjangka, Deposito\\n💰 Kredit: KPR, Kredit Usaha, Kredit Konsumtif\\n🏦 Digital: Mobile Banking, Internet Banking, ATM Network\\n\\nCabang Utama:\\n- Makassar (Pusat): (0411) 872234\\n- Bone: (0481) 21234\\n- Parepare: (0421) 22345\\n- Palopo: (0471) 23456\\n\\nPanduan Respons:\\n1. <PERSON><PERSON><PERSON> gunakan bahasa Indonesia yang sopan dan profesional\\n2. Berikan informasi yang akurat tentang Aistech\\n3. Jika tidak yakin, sarankan untuk menghubungi cabang\\n4. Untuk pertanyaan kompleks, tawarkan bantuan operator\\n5. Gunakan emoji yang sesuai untuk membuat percakapan lebih menarik\\n6. Berikan quick reply options yang relevan\\n\\nFormat respons JSON:\\n{\\n  \\\"response\\\": \\\"teks respons Anda\\\",\\n  \\\"quickReplies\\\": [\\n    {\\\"title\\\": \\\"Judul tombol\\\", \\\"payload\\\": \\\"payload\\\"}\\n  ],\\n  \\\"needsOperator\\\": false\\n}\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{{$json.userMessage}}\"\n    }\n  ],\n  \"temperature\": 0.7,\n  \"max_tokens\": 500,\n  \"top_p\": 1,\n  \"frequency_penalty\": 0,\n  \"presence_penalty\": 0\n}", "options": {}}, "id": "openrouter-ai", "name": "OpenRouter AI", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 500]}, {"parameters": {"jsCode": "// Process AI response and format for NestJS\nconst aiResponse = $input.first().json;\nconst chatRoomId = $node['Prepare AI Input'].json.chatRoomId;\n\nlet botResponse = '';\nlet quickReplies = [];\nlet actions = [];\n\ntry {\n  // Extract AI response\n  const aiContent = aiResponse.choices[0].message.content;\n  \n  // Try to parse as JSON first (if AI returns structured response)\n  try {\n    const parsedResponse = JSON.parse(aiContent);\n    botResponse = parsedResponse.response;\n    quickReplies = parsedResponse.quickReplies || [];\n    \n    if (parsedResponse.needsOperator) {\n      actions = [{\n        type: 'transfer_to_operator',\n        reason: 'AI determined user needs human operator'\n      }];\n    }\n  } catch (parseError) {\n    // If not JSON, use the raw response\n    botResponse = aiContent;\n    \n    // Add default quick replies\n    quickReplies = [\n      { title: '💳 Produk & Layanan', payload: 'products' },\n      { title: '🏦 Lokasi Cabang', payload: 'branches' },\n      { title: '📞 Hubungi Operator', payload: 'operator' }\n    ];\n  }\n  \n  // Check if AI response suggests operator transfer\n  if (botResponse.toLowerCase().includes('hubungi petugas') || \n      botResponse.toLowerCase().includes('operator') ||\n      botResponse.toLowerCase().includes('cabang terdekat')) {\n    quickReplies.push({ title: '📞 Hubungi Operator', payload: 'operator' });\n  }\n  \n} catch (error) {\n  // Fallback response if AI fails\n  botResponse = 'Maaf, saya mengalami kendala teknis. Silakan coba lagi atau hubungi operator kami untuk bantuan lebih lanjut.';\n  quickReplies = [\n    { title: '🔄 Coba Lagi', payload: 'retry' },\n    { title: '📞 Hubungi Operator', payload: 'operator' }\n  ];\n}\n\nreturn {\n  chatRoomId,\n  content: botResponse,\n  messageType: 'text',\n  metadata: {\n    quickReplies\n  },\n  actions\n};"}, "id": "process-ai-response", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 500]}, {"parameters": {"jsCode": "// Handle escalation request\nconst chatRoomId = $input.first().json.chatRoomId;\nconst userId = $input.first().json.userId;\n\nreturn {\n  chatRoomId,\n  content: 'Per<PERSON>taan Anda untuk berbicara dengan petugas sedang diproses. Mohon tunggu sebentar...',\n  messageType: 'text',\n  actions: [{\n    type: 'transfer_to_operator',\n    reason: 'User escalation request from bot'\n  }]\n};"}, "id": "handle-escalation", "name": "Handle Escalation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 700]}, {"parameters": {"url": "={{$node['Generate Welcome Message'].json.nestjsUrl || 'http://localhost:5000'}}/api/v1/n8n-hooks/bot-message", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "httpMethod": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-N8N-Signature", "value": "={{$node['Generate Welcome Message'].json.signature || 'your_webhook_signature'}}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "chatRoomId", "value": "={{$json.chatRoomId}}"}, {"name": "content", "value": "={{$json.content}}"}, {"name": "messageType", "value": "={{$json.messageType}}"}, {"name": "metadata", "value": "={{$json.metadata}}"}, {"name": "actions", "value": "={{$json.actions}}"}]}, "options": {}}, "id": "send-to-nestjs", "name": "Send Bot Message to NestJS", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 400]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.actions && $json.actions.length > 0}}", "value2": true}]}}, "id": "check-actions", "name": "Check for Actions", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1120, 400]}, {"parameters": {"url": "={{$node['Generate Welcome Message'].json.nestjsUrl || 'http://localhost:5000'}}/api/v1/n8n-hooks/chat-action", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "httpMethod": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-N8N-Signature", "value": "={{$node['Generate Welcome Message'].json.signature || 'your_webhook_signature'}}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "chatRoomId", "value": "={{$json.chatRoomId}}"}, {"name": "action", "value": "={{$json.actions[0].type}}"}, {"name": "reason", "value": "={{$json.actions[0].reason}}"}, {"name": "metadata", "value": "={{$json.actions[0].data}}"}]}, "options": {}}, "id": "execute-action", "name": "Execute Chat Action", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1340, 300]}, {"parameters": {"jsCode": "// Log successful completion\nconst result = $input.first().json;\nconsole.log('N8N Workflow completed successfully:', result);\n\nreturn {\n  success: true,\n  timestamp: new Date().toISOString(),\n  result\n};"}, "id": "log-completion", "name": "Log Completion", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 400]}], "connections": {"Chat Webhook Trigger": {"main": [[{"node": "Check Event Type", "type": "main", "index": 0}]]}, "Check Event Type": {"main": [[{"node": "Generate Welcome Message", "type": "main", "index": 0}], [{"node": "Check Message Event", "type": "main", "index": 0}]]}, "Check Message Event": {"main": [[{"node": "Prepare AI Input", "type": "main", "index": 0}], [{"node": "Check Escalation Request", "type": "main", "index": 0}]]}, "Prepare AI Input": {"main": [[{"node": "OpenRouter AI", "type": "main", "index": 0}]]}, "OpenRouter AI": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}, "Process AI Response": {"main": [[{"node": "Send Bot Message to NestJS", "type": "main", "index": 0}]]}, "Check Escalation Request": {"main": [[{"node": "Handle Escalation", "type": "main", "index": 0}]]}, "Generate Welcome Message": {"main": [[{"node": "Send Bot Message to NestJS", "type": "main", "index": 0}]]}, "Process User Message": {"main": [[{"node": "Send Bot Message to NestJS", "type": "main", "index": 0}]]}, "Handle Escalation": {"main": [[{"node": "Send Bot Message to NestJS", "type": "main", "index": 0}]]}, "Send Bot Message to NestJS": {"main": [[{"node": "Check for Actions", "type": "main", "index": 0}]]}, "Check for Actions": {"main": [[{"node": "Execute Chat Action", "type": "main", "index": 0}], [{"node": "Log Completion", "type": "main", "index": 0}]]}, "Execute Chat Action": {"main": [[{"node": "Log Completion", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-06T05:38:00.000Z", "updatedAt": "2025-01-06T05:38:00.000Z", "id": "Aistech", "name": "Aistech"}], "triggerCount": 1, "updatedAt": "2025-01-06T05:38:00.000Z", "versionId": "1"}