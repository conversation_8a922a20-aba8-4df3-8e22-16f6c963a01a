{"name": "My workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "chat-webhook", "options": {}}, "id": "cc7f1bb9-55eb-44b7-90d5-769d6a0f688a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-480, 0], "webhookId": "Aistech-chat"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.eventType}}", "value2": "new_chat"}]}}, "id": "07ec39e3-e1c5-44c1-be67-35e89a169d50", "name": "Check Event Type", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-260, 0]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.eventType}}", "value2": "new_message"}]}}, "id": "ae2c9a84-b62e-4376-8a87-e6c9b3635c20", "name": "Check Message Event", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-260, 220]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.eventType}}", "value2": "user_escalation_request"}]}}, "id": "44038d45-84af-4fd6-b16a-17265b651be2", "name": "Check Escalation Request", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-260, 460]}, {"parameters": {"jsCode": "// Welcome message for new chat\nconst chatRoomId = $input.first().json.chatRoomId;\nconst userId = $input.first().json.userId;\nconst metadata = $input.first().json.metadata;\n\n// Generate welcome message\nconst welcomeMessage = `Selamat datang di Aistech! 👋\\n\\nSaya adalah asisten virtual yang siap membantu Anda \\nSilakan ketik pertanyaan Anda atau ketik \"operator\" jika ingin berbicara dengan petugas kami.`;\n\nreturn {\n  chatRoomId,\n  userId,\n  content: welcomeMessage,\n  messageType: 'text',\n  metadata: {\n    quickReplies: [\n      { title: '💳 Produk & Layanan', payload: 'products' },\n      { title: '📞 Hubungi Operator', payload: 'operator' }\n    ]\n  }\n};"}, "id": "7e88c224-7099-4247-8a55-f2413e74bec2", "name": "Generate Welcome Message", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-40, -100]}, {"parameters": {"jsCode": "// Prepare user message for AI processing\nconst message = $input.first().json.message;\nconst chatRoomId = $input.first().json.chatRoomId;\nconst userId = $input.first().json.userId;\nconst content = message.content;\n\n// Check for explicit operator request first\nif (content.toLowerCase().includes('operator') || content.toLowerCase().includes('petugas') || content.toLowerCase().includes('manusia')) {\n  return {\n    chatRoomId,\n    content: 'Baik, saya akan menghubungkan Anda dengan petugas kami. Mohon tunggu sebentar...',\n    messageType: 'text',\n    actions: [{\n      type: 'transfer_to_operator',\n      reason: 'User requested human operator'\n    }]\n  };\n}\n\n// Prepare input for AI Agent - format as expected by LangChain Agent\nconst systemPrompt = `Anda adalah asisten virtual untuk Aistech Bank Sulsel. Tugas Anda adalah membantu nasabah dengan pertanyaan tentang produk dan layanan perbankan.\n\nPedoman:\n- Berikan jawaban yang informatif dan membantu\n- Jika pertanyaan di luar kemampuan <PERSON>, sarankan untuk menghubungi petugas\n- Gunakan bahasa Indonesia yang sopan dan profesional\n- Jika nasabah memerlukan bantuan khusus, tawarkan untuk menghubungkan dengan operator`;\n\nreturn {\n  chatRoomId,\n  userId,\n  input: content,\n  chat_history: [],\n  messageType: 'text'\n};"}, "id": "1585dea1-d610-4d3a-bc56-dcece87e536b", "name": "Prepare AI Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-40, 200]}, {"parameters": {"jsCode": "// Process AI Agent response and format for NestJS\nconst aiResponse = $input.first().json;\nconst chatRoomId = $node['Prepare AI Input'].json.chatRoomId;\n\nlet botResponse = '';\nlet quickReplies = [];\nlet actions = [];\n\ntry {\n  // AI Agent returns response in 'output' or 'text' field\n  const aiContent = aiResponse.output || aiResponse.text || aiResponse.response || '';\n  \n  // Try to parse as JSON first (if AI returns structured response)\n  try {\n    const parsedResponse = JSON.parse(aiContent);\n    botResponse = parsedResponse.response || parsedResponse.content || parsedResponse.text;\n    quickReplies = parsedResponse.quickReplies || [];\n    \n    if (parsedResponse.needsOperator) {\n      actions = [{\n        type: 'transfer_to_operator',\n        reason: 'AI determined user needs human operator'\n      }];\n    }\n  } catch (parseError) {\n    // If not JSON, use the raw response\n    botResponse = aiContent;\n    \n    // Add default quick replies\n    quickReplies = [\n      { title: '💳 Produk & Layanan', payload: 'products' }\n    ];\n  }\n  \n  // Ensure we have a response\n  if (!botResponse || botResponse.trim() === '') {\n    botResponse = 'Maaf, saya tidak dapat memahami pertanyaan Anda. Bisakah Anda mengulanginya dengan cara yang berbeda?';\n  }\n  \n  // Check if AI response suggests operator transfer\n  if (botResponse.toLowerCase().includes('hubungi petugas') || \n      botResponse.toLowerCase().includes('operator') ||\n      botResponse.toLowerCase().includes('cabang terdekat')) {\n    quickReplies.push({ title: '📞 Hubungi Operator', payload: 'operator' });\n  }\n  \n} catch (error) {\n  // Fallback response if AI fails\n  botResponse = 'Maaf, saya mengalami kendala teknis. Silakan coba lagi atau hubungi operator kami untuk bantuan lebih lanjut.';\n  quickReplies = [\n    { title: '🔄 Coba Lagi', payload: 'retry' },\n    { title: '📞 Hubungi Operator', payload: 'operator' }\n  ];\n}\n\nreturn {\n  chatRoomId,\n  content: botResponse,\n  messageType: 'text',\n  metadata: {\n    quickReplies\n  },\n  actions\n};"}, "id": "d044aaf2-9b62-45fc-ab25-e5e2033a6dac", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [620, 200]}, {"parameters": {"jsCode": "// Handle escalation request\nconst chatRoomId = $input.first().json.chatRoomId;\nconst userId = $input.first().json.userId;\n\nreturn {\n  chatRoomId,\n  content: 'Per<PERSON>taan Anda untuk berbicara dengan petugas sedang diproses. Mohon tunggu sebentar...',\n  messageType: 'text',\n  actions: [{\n    type: 'transfer_to_operator',\n    reason: 'User escalation request from bot'\n  }]\n};"}, "id": "1df7e624-bd32-4c7d-9924-667eea226993", "name": "Handle Escalation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-40, 400]}, {"parameters": {"jsCode": "// Prepare and sign bot message payload\nconst crypto = require('crypto');\n\n// IMPORTANT: Replace with your actual 64-character secret key\n// This must match the secret used in your NestJS backend\nconst secret = process.env.N8N_WEBHOOK_SECRET || 'your_actual_64_character_secret_key_replace_this_with_real_secret';\nconst nestjsBaseUrl = process.env.NESTJS_URL || 'http://localhost:5000';\n\n// Construct the exact object that will be sent as the body\nconst messageBody = {\n  chatRoomId: $json.chatRoomId,\n  content: $json.content,\n  messageType: $json.messageType,\n  metadata: $json.metadata,\n  actions: $json.actions\n};\n\n// Stringify the body object to create the payload for HMAC\nconst payloadString = JSON.stringify(messageBody);\n\n// Generate the HMAC SHA256 signature\nconst signature = crypto\n  .createHmac('sha256', secret)\n  .update(payloadString)\n  .digest('hex');\n\n// Return data needed by the HTTP Request node\nreturn {\n  requestUrl: `${nestjsBaseUrl}/api/v1/n8n-hooks/bot-message`,\n  generatedSignature: signature,\n  requestBody: messageBody\n};"}, "id": "a1b2c3d4-e5f6-4567-8901-234567890abc", "name": "Prepare & Sign Bot Message", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [200, -50]}, {"parameters": {"url": "={{$node['Prepare & Sign Bot Message'].json.requestUrl}}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-N8N-Signature", "value": "={{$node['Prepare & Sign Bot Message'].json.generatedSignature}}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "chatRoomId", "value": "={{$node['Prepare & Sign Bot Message'].json.requestBody.chatRoomId}}"}, {"name": "content", "value": "={{$node['Prepare & Sign Bot Message'].json.requestBody.content}}"}, {"name": "messageType", "value": "={{$node['Prepare & Sign Bot Message'].json.requestBody.messageType}}"}, {"name": "metadata", "value": "={{$node['Prepare & Sign Bot Message'].json.requestBody.metadata}}"}, {"name": "actions", "value": "={{$node['Prepare & Sign Bot Message'].json.requestBody.actions}}"}]}, "options": {}}, "id": "827d2879-b385-4677-83b2-5400209e0fcd", "name": "Send Bot Message to NestJS", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [400, 20], "credentials": {"httpHeaderAuth": {"id": "v7HJvHSYbIrrRfAc", "name": "Header Auth account"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.actions && $json.actions.length > 0}}", "value2": true}]}}, "id": "bd63fbe8-f745-4e15-bdf3-4540564291ed", "name": "Check for Actions", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [620, 20]}, {"parameters": {"jsCode": "// Prepare and sign chat action payload\nconst crypto = require('crypto');\n\n// IMPORTANT: Replace with your actual 64-character secret key\n// This must match the secret used in your NestJS backend and the same as in 'Prepare & Sign Bot Message'\nconst secret = process.env.N8N_WEBHOOK_SECRET || 'your_actual_64_character_secret_key_replace_this_with_real_secret';\nconst nestjsBaseUrl = process.env.NESTJS_URL || 'http://localhost:5000';\n\n// Extract action data from the input\nconst actionData = $json.actions && $json.actions.length > 0 ? $json.actions[0] : {};\n\n// Construct the exact object that will be sent as the body\nconst actionBody = {\n  chatRoomId: $json.chatRoomId,\n  action: actionData.type,\n  reason: actionData.reason,\n  metadata: actionData.data\n};\n\n// Stringify the body object to create the payload for HMAC\nconst payloadString = JSON.stringify(actionBody);\n\n// Generate the HMAC SHA256 signature\nconst signature = crypto\n  .createHmac('sha256', secret)\n  .update(payloadString)\n  .digest('hex');\n\n// Return data needed by the HTTP Request node\nreturn {\n  requestUrl: `${nestjsBaseUrl}/api/v1/n8n-hooks/chat-action`,\n  generatedSignature: signature,\n  requestBody: actionBody\n};"}, "id": "b2c3d4e5-f6g7-5678-9012-345678901bcd", "name": "Prepare & Sign Chat Action", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [750, -50]}, {"parameters": {"url": "={{$node['Prepare & Sign Chat Action'].json.requestUrl}}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-N8N-Signature", "value": "={{$node['Prepare & Sign Chat Action'].json.generatedSignature}}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "chatRoomId", "value": "={{$node['Prepare & Sign Chat Action'].json.requestBody.chatRoomId}}"}, {"name": "action", "value": "={{$node['Prepare & Sign Chat Action'].json.requestBody.action}}"}, {"name": "reason", "value": "={{$node['Prepare & Sign Chat Action'].json.requestBody.reason}}"}, {"name": "metadata", "value": "={{$node['Prepare & Sign Chat Action'].json.requestBody.metadata}}"}]}, "options": {}}, "id": "ae68711b-9a3e-4468-b52b-a474b2a436a2", "name": "Execute Chat Action", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [950, -120], "credentials": {"httpHeaderAuth": {"id": "v7HJvHSYbIrrRfAc", "name": "Header Auth account"}}}, {"parameters": {"jsCode": "// Log successful completion\nconst result = $input.first().json;\nconsole.log('N8N Workflow completed successfully:', result);\n\nreturn {\n  success: true,\n  timestamp: new Date().toISOString(),\n  result\n};"}, "id": "77128c0b-c89d-4893-8f90-247125c261d5", "name": "Log Completion", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1160, 100]}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [180, 200], "id": "800015dc-7633-4afe-8c70-9fa8c2d029ab", "name": "AI Agent"}, {"parameters": {"model": "google/gemini-2.5-flash-preview-05-20", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [180, 420], "id": "65e4b241-1e95-4da9-9e1a-52c9623305f5", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "2Du6HbzqQRMdPNnU", "name": "OpenRouter account 2"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [300, 520], "id": "067e5c42-ed89-4f50-bba7-bea40f11ea01", "name": "Simple Memory"}], "pinData": {}, "connections": {"Chat Webhook Trigger": {"main": [[{"node": "Check Event Type", "type": "main", "index": 0}]]}, "Check Event Type": {"main": [[{"node": "Generate Welcome Message", "type": "main", "index": 0}], [{"node": "Check Message Event", "type": "main", "index": 0}]]}, "Check Message Event": {"main": [[{"node": "Prepare AI Input", "type": "main", "index": 0}], [{"node": "Check Escalation Request", "type": "main", "index": 0}]]}, "Prepare AI Input": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Process AI Response": {"main": [[{"node": "Prepare & Sign Bot Message", "type": "main", "index": 0}]]}, "Check Escalation Request": {"main": [[{"node": "Handle Escalation", "type": "main", "index": 0}]]}, "Generate Welcome Message": {"main": [[{"node": "Prepare & Sign Bot Message", "type": "main", "index": 0}]]}, "Handle Escalation": {"main": [[{"node": "Prepare & Sign Bot Message", "type": "main", "index": 0}]]}, "Prepare & Sign Bot Message": {"main": [[{"node": "Send Bot Message to NestJS", "type": "main", "index": 0}]]}, "Send Bot Message to NestJS": {"main": [[{"node": "Check for Actions", "type": "main", "index": 0}]]}, "Check for Actions": {"main": [[{"node": "Prepare & Sign Chat Action", "type": "main", "index": 0}], [{"node": "Log Completion", "type": "main", "index": 0}]]}, "Prepare & Sign Chat Action": {"main": [[{"node": "Execute Chat Action", "type": "main", "index": 0}]]}, "Execute Chat Action": {"main": [[{"node": "Log Completion", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "4a8bd0f7-cefb-4abc-b3b5-8984fca00cc7", "meta": {"templateCredsSetupCompleted": true, "instanceId": "4f8fdfd4ecda31fb31dffea8820398fe16ece504b24311a3652bc37f03444317"}, "id": "CPWqkku2avxGJEMt", "tags": [{"name": "Aistech", "id": "D89ccKgVCOOXmmMm", "createdAt": "2025-06-04T05:55:03.478Z", "updatedAt": "2025-06-04T05:55:03.478Z"}]}