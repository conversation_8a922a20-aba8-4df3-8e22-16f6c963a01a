# N8N Setup Guide for Bank Sulsel Chat Integration

## Overview

This guide provides step-by-step instructions to set up N8N workflow integration with the Bank Sulsel chat system. The integration enables intelligent chatbot responses, office hours routing, and seamless escalation to human operators.

## Prerequisites

1. **N8N Installation**: N8N server running (local or cloud)
2. **NestJS Backend**: Bank Sulsel backend running with N8N integration enabled
3. **OpenRouter Account**: API key from https://openrouter.ai
4. **Environment Variables**: Properly configured environment variables

## Step 1: Environment Configuration

### 1.1 Update .env file

```bash
# N8N Integration Settings
ENABLE_N8N_INTEGRATION=true
N8N_WEBHOOK_URL=http://localhost:5678/webhook/chat-webhook
N8N_OUTGOING_API_KEY=your_secure_api_key_here
N8N_INCOMING_WEBHOOK_SECRET=your_secure_webhook_secret_here

# Office Hours Configuration
OFFICE_TIMEZONE=Asia/Makassar
OFFICE_START_HOUR=8
OFFICE_END_HOUR=17
OFFICE_DAYS=0,1  # Sunday=0, Monday=1

# Holiday API (Optional - defaults to Indonesian API)
HOLIDAY_API_URL=https://libur.deno.dev/api?year=2025
HOLIDAY_API_KEY=
```

### 1.2 Generate Secure Secrets

```bash
# Generate webhook secret
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Generate API key
node -e "console.log(require('crypto').randomBytes(16).toString('hex'))"
```

## Step 2: N8N Workflow Setup

### 2.1 Import Workflow

1. Open N8N interface (usually http://localhost:5678)
2. Click "Import from file" or "Import from URL"
3. Import the workflow from `backend/docs/N8N_WORKFLOW_CONFIG.json`

### 2.2 Configure Webhook Trigger

1. Open the "Chat Webhook Trigger" node
2. Set the webhook path to: `chat-webhook`
3. Note the webhook URL (e.g., `http://localhost:5678/webhook/chat-webhook`)
4. Update your NestJS `.env` file with this URL

### 2.3 Configure HTTP Request Nodes

Update the following nodes with your NestJS backend URL:

#### "Send Bot Message to NestJS" Node:

```json
{
  "url": "http://localhost:5000/api/v1/n8n-hooks/bot-message",
  "headers": {
    "X-N8N-Signature": "{{your_webhook_signature}}",
    "Content-Type": "application/json"
  }
}
```

#### "Execute Chat Action" Node:

```json
{
  "url": "http://localhost:5000/api/v1/n8n-hooks/chat-action",
  "headers": {
    "X-N8N-Signature": "{{your_webhook_signature}}",
    "Content-Type": "application/json"
  }
}
```

### 2.4 Configure OpenRouter AI Integration

#### 2.4.1 Set up OpenRouter Credentials

1. Go to https://openrouter.ai and create an account
2. Generate an API key from your dashboard
3. In N8N, go to Settings > Credentials
4. Create a new credential of type "HTTP Header Auth"
5. Name it "openRouterApiKey"
6. Set the credential value to your OpenRouter API key

#### 2.4.2 Configure the OpenRouter AI Node

1. Open the "OpenRouter AI" node in your workflow
2. Set the authentication to use your "openRouterApiKey" credential
3. The node is pre-configured to use Claude 3.5 Sonnet, but you can change the model:

Available models:

- `anthropic/claude-3.5-sonnet` (Recommended for banking)
- `openai/gpt-4o`
- `google/gemini-pro-1.5`
- `meta-llama/llama-3.1-8b-instruct`

#### 2.4.3 Customize AI System Prompt

The AI is configured with Bank Sulsel-specific knowledge. You can modify the system prompt in the OpenRouter AI node to:

```json
{
  "role": "system",
  "content": "Anda adalah asisten virtual Bank Sulsel yang ramah dan profesional. Anda membantu nasabah dengan informasi perbankan, produk, dan layanan Bank Sulsel.\n\nInformasi Bank Sulsel:\n- Bank Pembangunan Daerah Sulawesi Selatan\n- Kantor Pusat: Jl. Dr. Ratulangi No. 16, Makassar\n- Jam operasional: Senin-Minggu 08:00-17:00 WITA\n\nProduk & Layanan:\n💳 Simpanan: Tabungan Reguler, Tabungan Berjangka, Deposito\n💰 Kredit: KPR, Kredit Usaha, Kredit Konsumtif\n🏦 Digital: Mobile Banking, Internet Banking, ATM Network\n\nCabang Utama:\n- Makassar (Pusat): (0411) 872234\n- Bone: (0481) 21234\n- Parepare: (0421) 22345\n- Palopo: (0471) 23456\n\nPanduan Respons:\n1. Selalu gunakan bahasa Indonesia yang sopan dan profesional\n2. Berikan informasi yang akurat tentang Bank Sulsel\n3. Jika tidak yakin, sarankan untuk menghubungi cabang\n4. Untuk pertanyaan kompleks, tawarkan bantuan operator\n5. Gunakan emoji yang sesuai untuk membuat percakapan lebih menarik\n6. Berikan quick reply options yang relevan\n\nFormat respons JSON:\n{\n  \"response\": \"teks respons Anda\",\n  \"quickReplies\": [\n    {\"title\": \"Judul tombol\", \"payload\": \"payload\"}\n  ],\n  \"needsOperator\": false\n}"
}
```

### 2.5 Configure Signature Generation

Add a new "Code" node before HTTP requests to generate signatures:

```javascript
// Generate HMAC signature for webhook authentication
const crypto = require('crypto');
const secret = 'your_secure_webhook_secret_here'; // Same as N8N_INCOMING_WEBHOOK_SECRET
const payload = JSON.stringify($json);
const signature = crypto
  .createHmac('sha256', secret)
  .update(payload)
  .digest('hex');

return {
  ...$json,
  signature: signature,
  nestjsUrl: 'http://localhost:5000', // Your NestJS backend URL
};
```

## Step 3: Workflow Logic Overview

### 3.1 Event Flow

```mermaid
graph TD
    A[NestJS Chat Event] --> B[N8N Webhook Trigger]
    B --> C{Event Type?}

    C -->|new_chat| D[Generate Welcome Message]
    C -->|new_message| E[Process User Message]
    C -->|user_escalation_request| F[Handle Escalation]

    D --> G[Send Bot Message to NestJS]
    E --> G
    F --> G

    G --> H{Has Actions?}
    H -->|Yes| I[Execute Chat Action]
    H -->|No| J[Log Completion]
    I --> J
```

### 3.2 Bot Response Logic

The workflow handles various user intents:

- **Operator Request**: Keywords like "operator", "petugas", "manusia"
- **Product Information**: Keywords like "produk", "layanan", "tabungan", "kredit"
- **FAQ**: Keywords like "faq", "pertanyaan", "bantuan"
- **Branch Locations**: Keywords like "cabang", "lokasi", "alamat"
- **Default Response**: Fallback with quick reply options

### 3.3 Quick Replies

The bot provides interactive quick reply buttons:

- 💳 Produk & Layanan
- 📞 Hubungi Operator
- ❓ FAQ
- 🏦 Lokasi Cabang

## Step 4: Testing the Integration

### 4.1 Test Webhook Connectivity

```bash
# Test N8N webhook endpoint
curl -X POST http://localhost:5678/webhook/chat-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "eventType": "new_chat",
    "chatRoomId": "test-room-123",
    "userId": "test-user-456",
    "timestamp": "2025-01-06T05:40:00.000Z",
    "source": "nestjs-chat"
  }'
```

### 4.2 Test NestJS Endpoints

```bash
# Test bot message endpoint
curl -X POST http://localhost:5000/api/v1/n8n-hooks/bot-message \
  -H "Content-Type: application/json" \
  -H "X-N8N-Signature: your_signature_here" \
  -d '{
    "chatRoomId": "test-room-123",
    "content": "Hello from N8N bot!",
    "messageType": "text"
  }'

# Test chat action endpoint
curl -X POST http://localhost:5000/api/v1/n8n-hooks/chat-action \
  -H "Content-Type: application/json" \
  -H "X-N8N-Signature: your_signature_here" \
  -d '{
    "chatRoomId": "test-room-123",
    "action": "transfer_to_operator",
    "reason": "User requested human operator"
  }'
```

### 4.3 Test Office Hours Logic

```bash
# Test during office hours
curl -X GET http://localhost:5000/api/health

# Check logs for office hours decisions
tail -f logs/application.log | grep "OfficeHoursService"
```

## Step 5: Production Deployment

### 5.1 Security Considerations

1. **Use HTTPS**: Ensure both N8N and NestJS use HTTPS in production
2. **Secure Secrets**: Store secrets in environment variables or secret management
3. **Network Security**: Restrict access between N8N and NestJS
4. **Rate Limiting**: Implement rate limiting on webhook endpoints

### 5.2 Monitoring and Logging

1. **N8N Execution Logs**: Monitor workflow executions in N8N interface
2. **NestJS Logs**: Monitor application logs for integration events
3. **Error Handling**: Set up alerts for failed webhook calls
4. **Performance Metrics**: Track response times and success rates

### 5.3 Scaling Considerations

1. **N8N Clustering**: Use N8N in queue mode for high availability
2. **Database Performance**: Optimize chat database queries
3. **Caching**: Implement caching for frequently accessed data
4. **Load Balancing**: Use load balancers for multiple NestJS instances

## Step 6: Customization

### 6.1 Adding New Bot Responses

Edit the "Process User Message" node to add new intents:

```javascript
// Add new intent handling
else if (content.includes('your_new_keyword')) {
  botResponse = 'Your custom response here';
  quickReplies = [
    { title: 'Option 1', payload: 'option1' },
    { title: 'Option 2', payload: 'option2' }
  ];
}
```

### 6.2 Integrating External APIs

Add HTTP Request nodes to call external services:

```javascript
// Example: Get account balance
if (content.includes('saldo') || content.includes('balance')) {
  // Call banking API
  const accountData = await callBankingAPI(userId);
  botResponse = `Saldo Anda: Rp ${accountData.balance}`;
}
```

### 6.3 Advanced Routing Logic

Modify the routing logic in NestJS ChatService:

```typescript
// Custom routing based on user profile
private async determineAdvancedRouting(user: ChatUser): Promise<string> {
  if (user.isPremium) {
    return 'priority_operator';
  }
  if (user.hasComplexQuery) {
    return 'specialist_operator';
  }
  return 'bot';
}
```

## Troubleshooting

### Common Issues

1. **Webhook Not Triggered**

   - Check N8N webhook URL configuration
   - Verify network connectivity
   - Check firewall settings

2. **Signature Validation Failed**

   - Ensure secrets match between N8N and NestJS
   - Check raw body parsing configuration
   - Verify signature generation algorithm

3. **Bot Not Responding**

   - Check N8N workflow execution logs
   - Verify HTTP request node configurations
   - Test individual nodes in N8N

4. **Office Hours Not Working**
   - Check timezone configuration
   - Verify working days format
   - Test holiday API connectivity

### Debug Commands

```bash
# Check N8N logs
docker logs n8n-container

# Check NestJS logs
tail -f logs/application.log

# Test webhook signature
node -e "
const crypto = require('crypto');
const secret = 'your_secret';
const payload = 'your_payload';
const signature = crypto.createHmac('sha256', secret).update(payload).digest('hex');
console.log(signature);
"
```

## Support

For technical support or questions:

1. Check the logs for error messages
2. Review the N8N workflow execution history
3. Test individual components separately
4. Consult the N8N documentation for advanced features

## Conclusion

This integration provides a robust, scalable chatbot solution for Bank Sulsel with intelligent routing, office hours awareness, and seamless human handoff capabilities. The modular design allows for easy customization and extension based on specific business requirements.
