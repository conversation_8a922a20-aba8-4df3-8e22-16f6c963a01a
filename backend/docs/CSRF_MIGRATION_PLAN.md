# CSRF Migration Plan: From Custom Implementation to Official Package

## Current Issues with Custom CSRF Implementation

### 1. Security Vulnerabilities

- **Fixed IV in encryption**: [`EncryptionService`](src/common/decorators/encrypt_decrypt.ts) uses a constant IV, which is a major security flaw
- **Predictable token generation**: CSRF tokens use simple hash of `sessionId + secretKey`
- **Header inconsistency**: Guard checks `xsrf-token` but CORS allows `x-csrf-token`
- **Manual session management**: Prone to implementation errors

### 2. Implementation Problems

- **Scattered validation logic**: CSRF validation duplicated in [`JwtAuthGuard`](src/common/guards/jwt-auth.guard.ts)
- **Custom encryption complexity**: Unnecessary encryption layer adds complexity
- **Maintenance burden**: Custom security code requires ongoing security reviews

## Benefits of Official `csrf-csrf` Package

### 1. Industry-Standard Security

- ✅ **Cryptographically secure tokens**: Uses proper randomization
- ✅ **Double-submit cookie pattern**: Industry-standard CSRF protection
- ✅ **Automatic token rotation**: Enhanced security through token refresh
- ✅ **Battle-tested**: Used by thousands of applications

### 2. Simplified Implementation

- ✅ **Middleware-based**: Clean separation of concerns
- ✅ **Automatic validation**: No manual token checking required
- ✅ **Flexible configuration**: Supports multiple token sources
- ✅ **TypeScript support**: Full type safety

## Migration Steps

### Phase 1: Install and Configure (✅ COMPLETED)

```bash
npm install csrf-csrf
```

### Phase 2: Implement New CSRF Middleware (✅ COMPLETED)

- Created [`CsrfMiddleware`](src/common/middleware/csrf.middleware.ts)
- Configured with secure defaults
- Added support for multiple token sources

### Phase 3: Update App Module

```typescript
// Add to app.module.ts
import { CsrfMiddleware } from './common/middleware/csrf.middleware';
import { CsrfController } from './common/controllers/csrf.controller';

@Module({
  controllers: [AppController, CsrfController],
  // ... existing configuration
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CsrfMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });

    consumer
      .apply(UserActivityLoggerMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
```

### Phase 4: Remove Old CSRF Implementation

1. **Remove custom CSRF service**: Delete [`CsrfService`](src/common/decorators/csrf_token.ts)
2. **Update JWT Guard**: Remove CSRF validation from [`JwtAuthGuard`](src/common/guards/jwt-auth.guard.ts)
3. **Clean up providers**: Remove `CsrfService` from app module providers
4. **Update controllers**: Remove old CSRF token endpoints

### Phase 5: Update Frontend Integration

```javascript
// Frontend: Get CSRF token
const response = await fetch('/csrf/token', {
  credentials: 'include',
});
const { csrfToken } = await response.json();

// Frontend: Use token in requests
await fetch('/api/protected-endpoint', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-CSRF-Token': csrfToken,
  },
  credentials: 'include',
  body: JSON.stringify(data),
});
```

## Configuration Options

### Environment Variables

```env
# Add to .env
CSRF_SECRET=your-super-secure-csrf-secret-key-min-32-chars
```

### Security Features

- **HttpOnly cookies**: Prevents XSS attacks
- **SameSite=Strict**: Prevents CSRF attacks
- **Secure flag**: HTTPS-only in production
- **Token rotation**: Automatic token refresh
- **Multiple token sources**: Headers, body, query params

## Testing the Migration

### 1. Test CSRF Token Generation

```bash
curl -c cookies.txt http://localhost:5000/csrf/token
```

### 2. Test Protected Endpoint

```bash
# This should fail without CSRF token
curl -b cookies.txt -X POST http://localhost:5000/api/protected

# This should succeed with CSRF token
curl -b cookies.txt -H "X-CSRF-Token: TOKEN_HERE" -X POST http://localhost:5000/api/protected
```

## Rollback Plan

If issues arise during migration:

1. **Revert app.module.ts**: Remove new middleware
2. **Restore old services**: Re-add `CsrfService` to providers
3. **Restore JWT guard**: Re-enable CSRF validation in guard
4. **Remove new files**: Delete new middleware and controller

## Performance Impact

### Before (Custom Implementation)

- ❌ Encryption/decryption overhead on every request
- ❌ Manual validation in multiple places
- ❌ Session ID lookup complexity

### After (Official Package)

- ✅ Optimized token generation and validation
- ✅ Single middleware execution
- ✅ Minimal computational overhead

## Security Improvements

| Feature                | Custom Implementation | Official Package         |
| ---------------------- | --------------------- | ------------------------ |
| Token Generation       | Predictable hash      | Cryptographically secure |
| IV Usage               | Fixed (vulnerable)    | Random per operation     |
| Token Validation       | Manual, error-prone   | Automatic, battle-tested |
| Session Binding        | Custom logic          | Industry standard        |
| Token Rotation         | Manual                | Automatic                |
| Multiple Token Sources | Limited               | Full support             |

## Conclusion

Migrating to the official `csrf-csrf` package provides:

- **Enhanced security** through industry-standard implementation
- **Reduced complexity** by removing custom crypto code
- **Better maintainability** with community-supported package
- **Improved performance** through optimized algorithms
- **Future-proofing** with automatic security updates

The migration is low-risk with a clear rollback plan and provides immediate security benefits.
