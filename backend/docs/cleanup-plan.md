# Package Cleanup Plan

## 🎯 Summary

- **Unused Dependencies**: 8 packages
- **Unused DevDependencies**: 6 packages
- **Missing Dependencies**: 4 packages (need to install)
- **Estimated Savings**: ~15-20MB in node_modules

## 📦 Step 1: Remove Unused Dependencies

### Dependencies to Remove:

```bash
npm uninstall @nestjs-modules/ioredis @types/multer cache-manager-ioredis crypto-js nestjs-seeder passport-oauth2 pgsql rimraf
```

### DevDependencies to Remove:

```bash
npm uninstall --save-dev @nestjs/schematics @types/jest eslint-config-prettier eslint-plugin-prettier ts-loader
```

## 📦 Step 2: Install Missing Dependencies

```bash
npm install dotenv cookie-parser
npm install --save-dev @types/express
```

## 🧹 Step 3: Clean Up Unused Imports (Already Done by ESLint)

ESLint has already automatically fixed unused imports with the `--fix` flag.

## ✅ Step 4: Verification

After cleanup:

1. `npm install` - Clean install
2. `npm run build` - Verify build works
3. `npm run test` - Verify tests pass
4. `npm run start:dev` - Verify app starts

## 📊 Before vs After

### Before:

- Dependencies: 49 packages
- DevDependencies: 18 packages
- Total: 67 packages

### After:

- Dependencies: 43 packages (-6)
- DevDependencies: 14 packages (-4)
- Total: 57 packages (-10 packages, ~15% reduction)
