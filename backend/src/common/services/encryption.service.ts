/**
 * Secure Encryption Service
 * OWASP Reference: https://cheatsheetseries.owasp.org/cheatsheets/Cryptographic_Storage_Cheat_Sheet.html
 */

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as bcrypt from 'bcrypt';

interface EncryptionResult {
  encrypted: string;
  iv: string;
  tag: string;
}

interface DecryptionInput {
  encrypted: string;
  iv: string;
  tag: string;
}

@Injectable()
export class SecureEncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32; // 256 bits
  private readonly ivLength = 16; // 128 bits
  private readonly tagLength = 16; // 128 bits
  private readonly saltRounds = 12; // bcrypt rounds
  
  private readonly encryptionKey: Buffer;

  constructor(private configService: ConfigService) {
    const key = this.configService.get<string>('ENCRYPTION_KEY');
    if (!key) {
      throw new Error('ENCRYPTION_KEY environment variable is required');
    }
    
    if (key.length < 64) { // 32 bytes = 64 hex characters
      throw new Error('ENCRYPTION_KEY must be at least 64 hex characters (32 bytes)');
    }
    
    this.encryptionKey = Buffer.from(key, 'hex');
  }

  /**
   * Encrypt sensitive data using AES-256-GCM
   */
  encrypt(plaintext: string): EncryptionResult {
    try {
      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipher(this.algorithm, this.encryptionKey);
      cipher.setAAD(Buffer.from('additional-auth-data')); // Additional authenticated data
      
      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      return {
        encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex'),
      };
    } catch (error) {
      throw new Error(`Encryption failed: ${error.message}`);
    }
  }

  /**
   * Decrypt data encrypted with AES-256-GCM
   */
  decrypt(data: DecryptionInput): string {
    try {
      const decipher = crypto.createDecipher(this.algorithm, this.encryptionKey);
      decipher.setAAD(Buffer.from('additional-auth-data'));
      decipher.setAuthTag(Buffer.from(data.tag, 'hex'));
      
      let decrypted = decipher.update(data.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      throw new Error(`Decryption failed: ${error.message}`);
    }
  }

  /**
   * Hash password using bcrypt with salt
   */
  async hashPassword(password: string): Promise<string> {
    try {
      return await bcrypt.hash(password, this.saltRounds);
    } catch (error) {
      throw new Error(`Password hashing failed: ${error.message}`);
    }
  }

  /**
   * Verify password against hash
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      throw new Error(`Password verification failed: ${error.message}`);
    }
  }

  /**
   * Generate cryptographically secure random token
   */
  generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate secure API key
   */
  generateApiKey(): string {
    const timestamp = Date.now().toString(36);
    const randomPart = crypto.randomBytes(32).toString('hex');
    return `${timestamp}_${randomPart}`;
  }

  /**
   * Hash sensitive data for storage (one-way)
   */
  hashSensitiveData(data: string): string {
    const salt = crypto.randomBytes(16);
    const hash = crypto.pbkdf2Sync(data, salt, 100000, 64, 'sha512');
    return `${salt.toString('hex')}:${hash.toString('hex')}`;
  }

  /**
   * Verify hashed sensitive data
   */
  verifySensitiveData(data: string, hash: string): boolean {
    try {
      const [saltHex, hashHex] = hash.split(':');
      const salt = Buffer.from(saltHex, 'hex');
      const originalHash = Buffer.from(hashHex, 'hex');
      
      const computedHash = crypto.pbkdf2Sync(data, salt, 100000, 64, 'sha512');
      
      return crypto.timingSafeEqual(originalHash, computedHash);
    } catch (error) {
      return false;
    }
  }

  /**
   * Encrypt PII data with additional metadata
   */
  encryptPII(data: string, context: string = 'general'): string {
    const metadata = {
      context,
      timestamp: Date.now(),
      version: '1.0',
    };
    
    const payload = JSON.stringify({
      data,
      metadata,
    });
    
    const encrypted = this.encrypt(payload);
    
    // Combine all parts into a single string
    return `${encrypted.iv}:${encrypted.tag}:${encrypted.encrypted}`;
  }

  /**
   * Decrypt PII data and validate metadata
   */
  decryptPII(encryptedData: string): { data: string; metadata: any } {
    try {
      const [iv, tag, encrypted] = encryptedData.split(':');
      
      if (!iv || !tag || !encrypted) {
        throw new Error('Invalid encrypted data format');
      }
      
      const decrypted = this.decrypt({ iv, tag, encrypted });
      const payload = JSON.parse(decrypted);
      
      if (!payload.data || !payload.metadata) {
        throw new Error('Invalid payload structure');
      }
      
      return payload;
    } catch (error) {
      throw new Error(`PII decryption failed: ${error.message}`);
    }
  }

  /**
   * Securely wipe sensitive data from memory
   */
  secureWipe(buffer: Buffer): void {
    if (buffer && buffer.length > 0) {
      crypto.randomFillSync(buffer);
      buffer.fill(0);
    }
  }

  /**
   * Generate encryption key for environment setup
   */
  static generateEncryptionKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Validate encryption key format
   */
  static validateEncryptionKey(key: string): boolean {
    if (!key || typeof key !== 'string') {
      return false;
    }
    
    // Check if it's a valid hex string of correct length
    const hexRegex = /^[0-9a-fA-F]{64}$/;
    return hexRegex.test(key);
  }
}

/**
 * Data Classification Levels
 */
export enum DataClassification {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  CONFIDENTIAL = 'confidential',
  RESTRICTED = 'restricted',
}

/**
 * PII Data Types for GDPR/CCPA compliance
 */
export enum PIIDataType {
  NAME = 'name',
  EMAIL = 'email',
  PHONE = 'phone',
  ADDRESS = 'address',
  SSN = 'ssn',
  CREDIT_CARD = 'credit_card',
  BIOMETRIC = 'biometric',
  HEALTH = 'health',
  FINANCIAL = 'financial',
}

/**
 * Decorator for automatic PII encryption
 */
export function EncryptPII(dataType: PIIDataType) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      // Implementation would depend on specific use case
      // This is a placeholder for the decorator pattern
      return originalMethod.apply(this, args);
    };
    
    return descriptor;
  };
}

/**
 * Utility functions for data protection
 */
export class DataProtectionUtils {
  /**
   * Mask sensitive data for logging
   */
  static maskSensitiveData(data: string, visibleChars: number = 4): string {
    if (!data || data.length <= visibleChars) {
      return '*'.repeat(data?.length || 0);
    }
    
    const visible = data.slice(-visibleChars);
    const masked = '*'.repeat(data.length - visibleChars);
    return masked + visible;
  }

  /**
   * Validate if data contains PII patterns
   */
  static containsPII(data: string): boolean {
    const piiPatterns = [
      /\b\d{3}-\d{2}-\d{4}\b/, // SSN
      /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/, // Credit card
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email
      /\b\d{3}[\s-]?\d{3}[\s-]?\d{4}\b/, // Phone number
    ];
    
    return piiPatterns.some(pattern => pattern.test(data));
  }

  /**
   * Sanitize data for safe logging
   */
  static sanitizeForLogging(obj: any): any {
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'ssn', 'creditCard'];
    
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }
    
    const sanitized = { ...obj };
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = this.maskSensitiveData(sanitized[field]);
      }
    }
    
    return sanitized;
  }
}
